/**
 * Digital Skills Assessment Results Modal - Specific Styles
 * Extends unified-assessment-modal.css with digital skills specific components
 * Uses 8px spacing grid and existing application theme
 */

/* Digital Skills Level Progression - Specific to this assessment type */
.digital-skills-progression-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.digital-skills-level-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    transition: all 0.2s ease;
}

.digital-skills-level-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.digital-skills-level-item.passed {
    background: rgba(76, 175, 80, 0.1);
    border-color: #4CAF50;
}

.digital-skills-level-item.failed {
    background: rgba(244, 67, 54, 0.1);
    border-color: #f44336;
}

.digital-skills-level-item.current {
    background: rgba(33, 150, 243, 0.1);
    border-color: #2196F3;
}

.digital-skills-level-item.not-attempted {
    background: #f8f9fa;
    border-color: #e0e0e0;
}

.digital-skills-level-icon {
    font-size: 24px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.digital-skills-level-info {
    flex: 1;
}

.digital-skills-level-name {
    color: #333333;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
}

.digital-skills-level-status {
    color: #666666;
    font-size: 14px;
}

/* Responsive Design for Digital Skills Specific Elements */
@media (max-width: 768px) {
    .digital-skills-level-item {
        padding: 12px;
        gap: 12px;
    }
    
    .digital-skills-level-icon {
        font-size: 20px;
        width: 28px;
        height: 28px;
    }
    
    .digital-skills-level-name {
        font-size: 14px;
    }
    
    .digital-skills-level-status {
        font-size: 12px;
    }
}
