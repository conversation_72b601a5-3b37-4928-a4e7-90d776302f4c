/**
 * Mathematics Assessment Review Modal
 * Comprehensive modal for admin review of detailed mathematics assessment responses
 * Includes transparency features and manual level override functionality
 */

(function() {
    'use strict';

    let isReviewModalInitialized = false;
    let currentReviewData = null;
    let currentUserEmail = null;
    let currentUserCompany = null;

    // Public API
    window.MathAssessmentReviewModal = {
        show: showAssessmentReviewModal,
        hide: hideReviewModal
    };

    /**
     * Show Mathematics assessment review modal with detailed responses
     * @param {Object} mathData - Mathematics assessment data (for compatibility)
     * @param {string} userEmail - User's email
     * @param {string} userName - User's name
     * @param {string} userCompany - Company ID
     */
    async function showAssessmentReviewModal(mathData, userEmail, userName, userCompany) {
        try {
            // Validate required parameters
            if (!validateReviewModalParameters(userEmail, userName, userCompany)) {
                throw new Error('Invalid parameters provided to mathematics assessment review modal');
            }

            // Show loading overlay
            if (typeof showLoadingOverlay === 'function') {
                showLoadingOverlay();
            }

            // Store current user info for level override functionality
            currentUserEmail = userEmail;
            currentUserCompany = userCompany;

            // Fetch detailed assessment data with validation
            const reviewData = await fetchDetailedAssessmentData(userEmail, userCompany);
            currentReviewData = validateAndEnhanceReviewData(reviewData, userEmail, userName);

            if (isReviewModalInitialized) {
                await resetAndShowReviewModal(userName);
                return;
            }

            // Create modal if it doesn't exist
            await createReviewModal(userName);
            isReviewModalInitialized = true;

        } catch (error) {
            console.error('Error showing Mathematics assessment review modal:', error);
            if (typeof hideLoadingOverlay === 'function') {
                hideLoadingOverlay();
            }
            if (typeof showNotification === 'function') {
                showNotification('Failed to load mathematics assessment details', 'error');
            }

            // Show fallback review modal with error state
            await showFallbackReviewModal(userName, error.message);
        }
    }

    /**
     * Hide the review modal
     */
    function hideReviewModal() {
        const overlay = document.getElementById('math-review-overlay');
        if (overlay) {
            overlay.style.opacity = '0';
            setTimeout(() => {
                overlay.remove();
                isReviewModalInitialized = false;
            }, 300);
        }
    }

    /**
     * Fetch detailed assessment data for admin review
     */
    async function fetchDetailedAssessmentData(userEmail, userCompany) {
        try {
            if (typeof db === 'undefined') {
                throw new Error('Database not initialized');
            }

            // Validate parameters
            if (!userEmail || !userCompany) {
                throw new Error('Missing required parameters: userEmail or userCompany');
            }

            console.log('Fetching math data for:', { userEmail, userCompany });

            const userRef = db.collection('companies')
                             .doc(userCompany)
                             .collection('users')
                             .doc(userEmail);

            const userDoc = await userRef.get();
            if (!userDoc.exists) {
                throw new Error('User document not found');
            }

            const userData = userDoc.data();
            console.log('Raw user data:', userData);

            // Log specific mathematics fields for debugging
            console.log('Mathematics fields:', {
                mathAssessmentCompleted: userData.mathAssessmentCompleted,
                mathCurrentLevel: userData.mathCurrentLevel,
                mathOverallScore: userData.mathOverallScore,
                mathLevel1: userData.mathLevel1,
                mathGCSEPart1: userData.mathGCSEPart1,
                mathAssessmentResponses: userData.mathAssessmentResponses
            });

            // Extract comprehensive mathematics assessment data following the complete schema
            const mathData = {
                // Core assessment fields
                mathAssessmentCompleted: userData.mathAssessmentCompleted || false,
                mathCurrentLevel: userData.mathCurrentLevel || null,
                mathOverallScore: userData.mathOverallScore || 0,
                mathHighestLevelCompleted: userData.mathHighestLevelCompleted || null,
                mathAssessmentTimestamp: userData.mathAssessmentTimestamp || null,
                totalTimeSpentOnMath: userData.totalTimeSpentOnMath || 0,
                updatedAt: userData.updatedAt || null,

                // Level-specific assessment data with complete structure
                mathEntryLevel: userData.mathEntryLevel || {
                    completed: false,
                    score: 0,
                    passed: false,
                    timeSpent: 0,
                    completedAt: null,
                    responses: [],
                    topicBreakdown: {
                        arithmetic: { score: 0, maxScore: 8 },
                        fractions: { score: 0, maxScore: 6 },
                        percentages: { score: 0, maxScore: 4 },
                        basicAlgebra: { score: 0, maxScore: 6 },
                        measurement: { score: 0, maxScore: 4 },
                        dataHandling: { score: 0, maxScore: 4 }
                    }
                },

                mathLevel1: userData.mathLevel1 || {
                    completed: false,
                    score: 0,
                    passed: false,
                    timeSpent: 0,
                    completedAt: null,
                    responses: [],
                    topicBreakdown: {
                        advancedArithmetic: { score: 0, maxScore: 4 },
                        fractionsDecimals: { score: 0, maxScore: 4 },
                        percentagesRatio: { score: 0, maxScore: 4 },
                        algebraicExpressions: { score: 0, maxScore: 6 },
                        geometry: { score: 0, maxScore: 4 },
                        statistics: { score: 0, maxScore: 4 }
                    }
                },

                mathGCSEPart1: userData.mathGCSEPart1 || {
                    completed: false,
                    score: 0,
                    passed: false,
                    timeSpent: 0,
                    completedAt: null,
                    responses: [],
                    topicBreakdown: {
                        numberOperations: { score: 0, maxScore: 3 },
                        algebraicManipulation: { score: 0, maxScore: 3 },
                        geometricReasoning: { score: 0, maxScore: 2 },
                        fractionalCalculations: { score: 0, maxScore: 2 }
                    }
                },

                mathGCSEPart2: userData.mathGCSEPart2 || {
                    completed: false,
                    score: 0,
                    passed: false,
                    timeSpent: 0,
                    completedAt: null,
                    responses: [],
                    topicBreakdown: {
                        complexCalculations: { score: 0, maxScore: 4 },
                        statisticalAnalysis: { score: 0, maxScore: 4 },
                        trigonometry: { score: 0, maxScore: 4 },
                        advancedAlgebra: { score: 0, maxScore: 4 },
                        problemSolving: { score: 0, maxScore: 4 }
                    }
                },

                // Enhanced feedback fields
                mathFeedback: userData.mathFeedback || getDefaultMathFeedback(userData.mathOverallScore || 0),
                mathStrengths: userData.mathStrengths || getDefaultMathStrengths(userData.mathOverallScore || 0),
                mathImprovements: userData.mathImprovements || getDefaultMathImprovements(userData.mathOverallScore || 0),
                mathPlacementRecommendation: userData.mathPlacementRecommendation || getDefaultPlacementRecommendation(userData.mathOverallScore || 0, userData.mathHighestLevelCompleted),

                // Comprehensive response logging for admin transparency
                mathAssessmentResponses: userData.mathAssessmentResponses || {
                    questionResponses: [],
                    assessmentMetadata: {
                        sessionId: null,
                        userAgent: null,
                        startTime: null,
                        endTime: null,
                        totalInteractions: 0,
                        calculatorUsed: false,
                        assistiveTechnologyUsed: false,
                        browserInfo: {}
                    },
                    interactionLog: []
                },

                // Manual override tracking
                manualScoreOverride: userData.manualScoreOverride || null,
                manualLevelOverride: userData.manualLevelOverride || null,
                overrideHistory: userData.overrideHistory || [],

                // User identification
                userEmail: userEmail,
                firstName: userData.firstName || '',
                lastName: userData.lastName || '',
                userType: userData.userType || 'student',
                studentLevel: userData.studentLevel || 'adult-learner'
            };

            return mathData;

        } catch (error) {
            console.error('Error fetching detailed mathematics data:', error);
            return null;
        }
    }

    /**
     * Validate review modal parameters
     */
    function validateReviewModalParameters(userEmail, userName, userCompany) {
        if (!userEmail || typeof userEmail !== 'string' || !userEmail.includes('@')) {
            console.error('Invalid userEmail provided to review modal:', userEmail);
            return false;
        }

        if (!userName || typeof userName !== 'string' || userName.trim().length === 0) {
            console.error('Invalid userName provided to review modal:', userName);
            return false;
        }

        if (!userCompany || typeof userCompany !== 'string' || userCompany.trim().length === 0) {
            console.error('Invalid userCompany provided to review modal:', userCompany);
            return false;
        }

        return true;
    }

    /**
     * Validate and enhance review data with comprehensive fallbacks
     */
    function validateAndEnhanceReviewData(reviewData, userEmail, userName) {
        if (!reviewData || typeof reviewData !== 'object') {
            console.warn('No review data provided, creating fallback data');
            return createFallbackReviewData(userEmail, userName);
        }

        // Validate and enhance all review data fields
        const validatedData = {
            // Core assessment fields with validation
            mathAssessmentCompleted: Boolean(reviewData.mathAssessmentCompleted),
            mathCurrentLevel: validateReviewLevel(reviewData.mathCurrentLevel) || 'Entry',
            mathOverallScore: validateReviewScore(reviewData.mathOverallScore) || 0,
            mathHighestLevelCompleted: validateReviewLevel(reviewData.mathHighestLevelCompleted) || null,
            mathAssessmentTimestamp: validateReviewTimestamp(reviewData.mathAssessmentTimestamp) || null,
            totalTimeSpentOnMath: validateReviewTimeSpent(reviewData.totalTimeSpentOnMath) || 0,
            updatedAt: validateReviewTimestamp(reviewData.updatedAt) || null,

            // Level-specific data with comprehensive validation
            mathEntryLevel: validateReviewLevelData(reviewData.mathEntryLevel, 'Entry'),
            mathLevel1: validateReviewLevelData(reviewData.mathLevel1, 'Level1'),
            mathGCSEPart1: validateReviewLevelData(reviewData.mathGCSEPart1, 'GCSEPart1'),
            mathGCSEPart2: validateReviewLevelData(reviewData.mathGCSEPart2, 'GCSEPart2'),

            // Enhanced feedback fields with validation
            mathFeedback: validateReviewFeedback(reviewData.mathFeedback) || getDefaultMathFeedback(reviewData.mathOverallScore || 0),
            mathStrengths: validateReviewArray(reviewData.mathStrengths) || getDefaultMathStrengths(reviewData.mathOverallScore || 0),
            mathImprovements: validateReviewArray(reviewData.mathImprovements) || getDefaultMathImprovements(reviewData.mathOverallScore || 0),
            mathPlacementRecommendation: validateReviewPlacementRecommendation(reviewData.mathPlacementRecommendation) || getDefaultPlacementRecommendation(reviewData.mathOverallScore || 0, reviewData.mathHighestLevelCompleted),

            // Comprehensive response logging with validation
            mathAssessmentResponses: validateReviewAssessmentResponses(reviewData.mathAssessmentResponses),

            // Manual override tracking with validation
            manualScoreOverride: validateReviewOverride(reviewData.manualScoreOverride) || null,
            manualLevelOverride: validateReviewOverride(reviewData.manualLevelOverride) || null,
            overrideHistory: validateReviewArray(reviewData.overrideHistory) || [],

            // User identification with validation
            userEmail: userEmail,
            firstName: validateReviewString(reviewData.firstName) || userName.split(' ')[0] || '',
            lastName: validateReviewString(reviewData.lastName) || userName.split(' ').slice(1).join(' ') || '',
            userType: validateReviewString(reviewData.userType) || 'student',
            studentLevel: validateReviewString(reviewData.studentLevel) || 'adult-learner'
        };

        return validatedData;
    }

    /**
     * Create fallback review data when no data is available
     */
    function createFallbackReviewData(userEmail, userName) {
        return {
            mathAssessmentCompleted: false,
            mathCurrentLevel: 'Entry',
            mathOverallScore: 0,
            mathHighestLevelCompleted: null,
            mathAssessmentTimestamp: null,
            totalTimeSpentOnMath: 0,
            updatedAt: null,

            mathEntryLevel: createDefaultReviewLevelData('Entry'),
            mathLevel1: createDefaultReviewLevelData('Level1'),
            mathGCSEPart1: createDefaultReviewLevelData('GCSEPart1'),
            mathGCSEPart2: createDefaultReviewLevelData('GCSEPart2'),

            mathFeedback: getDefaultMathFeedback(0),
            mathStrengths: getDefaultMathStrengths(0),
            mathImprovements: getDefaultMathImprovements(0),
            mathPlacementRecommendation: getDefaultPlacementRecommendation(0, null),

            mathAssessmentResponses: {
                questionResponses: [],
                assessmentMetadata: {
                    sessionId: null,
                    userAgent: null,
                    startTime: null,
                    endTime: null,
                    totalInteractions: 0,
                    calculatorUsed: false,
                    assistiveTechnologyUsed: false,
                    browserInfo: {}
                },
                interactionLog: []
            },

            manualScoreOverride: null,
            manualLevelOverride: null,
            overrideHistory: [],

            userEmail: userEmail,
            firstName: userName.split(' ')[0] || '',
            lastName: userName.split(' ').slice(1).join(' ') || '',
            userType: 'student',
            studentLevel: 'adult-learner'
        };
    }

    /**
     * Show fallback review modal when data loading fails
     */
    async function showFallbackReviewModal(userName, errorMessage) {
        try {
            // Create minimal fallback data
            currentReviewData = createFallbackReviewData('<EMAIL>', userName);

            // Create modal with error state
            const modalHTML = `
                <div id="math-review-overlay" class="math-review-modal-overlay">
                    <div class="math-review-modal-content">
                        <div class="math-review-modal-header">
                            <div class="math-review-modal-title-container">
                                <h2 class="math-review-modal-employee-title">${userName}</h2>
                                <h3 class="math-review-modal-subtitle">Mathematics Assessment Review</h3>
                            </div>
                            <div class="math-review-modal-actions">
                                <button id="close-math-review-modal" class="math-review-close-modal-button">
                                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="math-review-modal-body">
                            <div class="math-review-error-content">
                                <div class="math-review-error-icon">
                                    <svg width="48" height="48" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                    </svg>
                                </div>
                                <h3>Assessment Review Data Unavailable</h3>
                                <p>Unable to load mathematics assessment data for admin review: ${errorMessage}</p>
                                <p>Please try again later or contact support if the problem persists.</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);
            addReviewModalStyles();

            const overlay = document.getElementById('math-review-overlay');
            const closeBtn = overlay.querySelector('#close-math-review-modal');
            if (closeBtn) {
                closeBtn.addEventListener('click', hideReviewModal);
            }

            overlay.addEventListener('click', (event) => {
                if (event.target.id === 'math-review-overlay') {
                    hideReviewModal();
                }
            });

            // Show modal
            overlay.style.display = 'flex';
            overlay.style.opacity = '0';

            if (typeof hideLoadingOverlay === 'function') {
                hideLoadingOverlay();
            }

            requestAnimationFrame(() => {
                setTimeout(() => {
                    overlay.style.opacity = '1';
                    const modalContent = overlay.querySelector('.math-review-modal-content');
                    if (modalContent) {
                        modalContent.style.opacity = '1';
                        modalContent.style.transform = 'scale(1)';
                    }
                }, 10);
            });

        } catch (fallbackError) {
            console.error('Error showing fallback review modal:', fallbackError);
            if (typeof hideLoadingOverlay === 'function') {
                hideLoadingOverlay();
            }
        }
    }

    /**
     * Generate default feedback based on score (following schema patterns)
     */
    function getDefaultMathFeedback(score) {
        if (score >= 80) {
            return {
                numericalSkills: 'Exceptional numerical accuracy across all operations. Demonstrates advanced calculation strategies and efficiency.',
                algebraicThinking: 'Strong algebraic reasoning with excellent manipulation skills. Comfortable with complex expressions and equations.',
                problemSolving: 'Outstanding problem-solving abilities. Systematically approaches complex problems with clear mathematical reasoning.',
                geometricReasoning: 'Excellent spatial understanding and geometric calculation accuracy. Strong grasp of advanced geometric concepts.',
                dataHandling: 'Advanced statistical analysis skills. Interprets complex data sets accurately and draws appropriate conclusions.',
                overall: 'Exceptional GCSE-level mathematics performance. Ready for advanced mathematical studies. Consider Champions level courses.'
            };
        } else if (score >= 60) {
            return {
                numericalSkills: 'Good numerical skills with accurate basic calculations. Minor issues with complex operations but overall demonstrates solid competency.',
                algebraicThinking: 'Solid understanding of algebraic concepts. Can solve linear equations effectively but needs practice with more complex expressions.',
                problemSolving: 'Effective approach to mathematical problem-solving. Shows logical reasoning with room for development in complex scenarios.',
                geometricReasoning: 'Good spatial awareness and geometric understanding. Area and perimeter calculations are generally accurate.',
                dataHandling: 'Adequate data handling and basic statistical skills. Can interpret simple charts and graphs effectively.',
                overall: 'Strong Level 1 mathematics performance. Ready for GCSE Part 1 with additional practice in algebraic manipulation and statistical analysis.'
            };
        } else if (score >= 40) {
            return {
                numericalSkills: 'Adequate numerical skills for everyday calculations. Some accuracy issues with complex operations that need attention.',
                algebraicThinking: 'Basic algebraic understanding with room for development. Requires practice with variable manipulation and equation solving.',
                problemSolving: 'Developing problem-solving strategies and mathematical reasoning. Shows potential with guided practice.',
                geometricReasoning: 'Foundational geometric concepts understood. Needs work on calculation accuracy and spatial reasoning.',
                dataHandling: 'Basic data interpretation skills demonstrated. Statistical concepts require further development and practice.',
                overall: 'Solid Entry level mathematics foundation. Some areas for improvement before advancing to higher levels. Additional support recommended.'
            };
        } else {
            return {
                numericalSkills: 'Basic numerical understanding with opportunities for improvement. Focus needed on fundamental arithmetic operations.',
                algebraicThinking: 'Limited algebraic understanding. Requires foundational work with variables and simple equations before progression.',
                problemSolving: 'Simple problem-solving approach with potential for enhancement. Needs practice breaking down problems into manageable steps.',
                geometricReasoning: 'Basic geometric awareness with room for growth. Shape recognition good but calculation skills need development.',
                dataHandling: 'Elementary data handling skills requiring further development. Practice needed with chart reading and basic statistics.',
                overall: 'Entry level mathematics proficiency identified. Recommend starting with Essentials level courses to build strong mathematical foundation.'
            };
        }
    }

    /**
     * Generate default strengths based on score
     */
    function getDefaultMathStrengths(score) {
        if (score >= 80) {
            return [
                'Advanced algebraic manipulation',
                'Excellent problem-solving strategies',
                'Strong statistical analysis skills',
                'Accurate complex calculations',
                'Clear mathematical reasoning',
                'Ready for advanced mathematical applications'
            ];
        } else if (score >= 60) {
            return [
                'Good mathematical foundation',
                'Solid numerical computation skills',
                'Effective basic problem-solving',
                'Clear understanding of key concepts',
                'Logical approach to mathematical problems'
            ];
        } else if (score >= 40) {
            return [
                'Adequate basic mathematical skills',
                'Understanding of fundamental concepts',
                'Willingness to engage with mathematical problems',
                'Shows mathematical curiosity and effort'
            ];
        } else {
            return [
                'Completed the mathematics assessment',
                'Basic mathematical concepts attempted',
                'Foundation for improvement established',
                'Shows mathematical curiosity'
            ];
        }
    }

    /**
     * Generate default improvements based on score
     */
    function getDefaultMathImprovements(score) {
        if (score >= 80) {
            return [
                'Continue practicing advanced mathematical techniques',
                'Explore specialized mathematical applications',
                'Maintain current proficiency level',
                'Consider advanced mathematics pathway'
            ];
        } else if (score >= 60) {
            return [
                'Practice complex problem-solving strategies',
                'Strengthen algebraic manipulation skills',
                'Work on advanced geometric concepts',
                'Develop statistical analysis techniques',
                'Consider additional mathematical support'
            ];
        } else if (score >= 40) {
            return [
                'Focus on strengthening numerical skills',
                'Practice algebraic problem-solving',
                'Work on geometric reasoning',
                'Develop data interpretation skills',
                'Consider mathematics support courses'
            ];
        } else {
            return [
                'Focus on basic numerical operations',
                'Build foundational mathematical vocabulary',
                'Practice simple problem-solving',
                'Work on arithmetic accuracy',
                'Consider mathematics foundation courses',
                'Regular mathematical practice recommended'
            ];
        }
    }

    /**
     * Generate default placement recommendation
     */
    function getDefaultPlacementRecommendation(score, highestLevel) {
        if (score >= 80 || highestLevel === 'GCSEPart2') {
            return {
                recommendedLevel: 'Champions',
                specificCourses: ['ICDL Level 3', 'Advanced Mathematical Applications', 'Computer Skills for Work Level 3'],
                reasoning: 'Exceptional performance across all assessment levels demonstrates readiness for advanced mathematical studies.',
                nextSteps: ['Enroll in Champions level courses', 'Consider advanced mathematics pathway', 'Explore specialized mathematical applications']
            };
        } else if (score >= 60 || highestLevel === 'GCSEPart1' || highestLevel === 'Level1') {
            return {
                recommendedLevel: 'Intermediate',
                specificCourses: ['ICDL Level 2', 'Computer Skills for Work Level 2', 'Improvers Plus'],
                reasoning: 'Strong mathematics foundation qualifies you for intermediate-level courses with practical applications.',
                nextSteps: ['Continue building on your solid mathematical foundation', 'Enroll in intermediate courses', 'Practice advanced problem-solving']
            };
        } else if (score >= 40 || highestLevel === 'Entry') {
            return {
                recommendedLevel: 'Improvers',
                specificCourses: ['Computer Skills Beginners Plus', 'Everyday Life Level 1', 'Mathematics Skills Development'],
                reasoning: 'Good basic mathematics skills qualify you for foundational courses and skill development.',
                nextSteps: ['Focus on strengthening mathematical foundation', 'Complete foundational courses', 'Practice regularly before advancing']
            };
        } else {
            return {
                recommendedLevel: 'Essentials',
                specificCourses: ['Computer Skills Beginners', 'Basic Numeracy Support', 'Foundation Mathematics'],
                reasoning: 'Entry-level assessment indicates need for foundational mathematical skills development before progressing to higher levels.',
                nextSteps: ['Complete Essentials level courses', 'Practice basic arithmetic daily', 'Retake assessment after skill development']
            };
        }
    }

    /**
     * Create review modal
     */
    async function createReviewModal(userName) {
        // Remove existing modal if any
        const existingOverlay = document.getElementById('math-review-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // Create modal HTML
        const modalHTML = createReviewModalHTML(userName);

        // Add to DOM
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Add styles if not already added
        addReviewModalStyles();

        // Initialize event listeners
        const overlay = document.getElementById('math-review-overlay');
        initializeReviewEventListeners(overlay);

        // Show modal with animation
        overlay.style.display = 'flex';
        overlay.style.opacity = '0';

        // Hide loading overlay
        if (typeof hideLoadingOverlay === 'function') {
            hideLoadingOverlay();
        }

        // Animate in with skills gap modal style
        requestAnimationFrame(() => {
            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.math-review-modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }
            }, 10);
        });
    }

    /**
     * Reset and show existing review modal
     */
    async function resetAndShowReviewModal(userName) {
        const overlay = document.getElementById('math-review-overlay');
        if (!overlay) return;

        // Rebuild modal HTML with new data
        overlay.innerHTML = createReviewModalContent(userName);

        // Re-initialize event listeners
        initializeReviewEventListeners(overlay);

        // Show modal with animation
        overlay.style.display = 'flex';
        overlay.style.opacity = '0';

        // Hide loading overlay
        if (typeof hideLoadingOverlay === 'function') {
            hideLoadingOverlay();
        }

        // Animate in with skills gap modal style
        requestAnimationFrame(() => {
            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.math-review-modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }
            }, 10);
        });
    }

    /**
     * Create review modal HTML structure
     */
    function createReviewModalHTML(userName) {
        return `
            <div id="math-review-overlay" class="math-review-modal-overlay" data-user-email="${currentUserEmail || ''}">
                ${createReviewModalContent(userName)}
            </div>
        `;
    }

    /**
     * Create review modal content
     */
    function createReviewModalContent(userName) {
        if (!currentReviewData) {
            return createErrorContent(userName);
        }

        // Determine display values based on overrides
        const originalScore = currentReviewData.mathOverallScore || 0;
        const originalLevel = currentReviewData.mathCurrentLevel || 'Entry';
        const hasScoreOverride = currentReviewData.manualScoreOverride;
        const hasLevelOverride = currentReviewData.manualLevelOverride;

        // Use overridden values if available
        const displayScore = hasScoreOverride ? currentReviewData.manualScoreOverride.newScore : originalScore;
        const displayLevel = hasScoreOverride ?
            (typeof window.calculateMathLevelFromScore === 'function' ?
                window.calculateMathLevelFromScore(currentReviewData.manualScoreOverride.newScore) :
                currentReviewData.manualScoreOverride.calculatedLevel) :
            (hasLevelOverride ? currentReviewData.manualLevelOverride.newLevel : originalLevel);

        const isQualified = displayScore >= 60;
        const score = displayScore;
        const level = displayLevel;
        const feedback = currentReviewData.mathFeedback || {};
        const strengths = currentReviewData.mathStrengths || [];
        const improvements = currentReviewData.mathImprovements || [];

        const timeSpent = currentReviewData.totalTimeSpentOnMath || 0;
        const timestamp = currentReviewData.mathAssessmentTimestamp;

        // Format timestamp
        const formattedDate = timestamp ? new Date(timestamp.toDate ? timestamp.toDate() : timestamp).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }) : 'Date not available';

        // Format time spent
        const formattedTime = timeSpent > 0 ? `${Math.round(timeSpent / 60)} minutes` : 'Time not recorded';

        return `
            <div class="math-review-modal-content">
                <div class="math-review-modal-header">
                    <div class="math-review-modal-title-container">
                        <h2 class="math-review-modal-employee-title">${userName}</h2>
                        <h3 class="math-review-modal-subtitle">Mathematics Assessment Review</h3>
                    </div>
                    <div class="math-review-modal-actions">
                        <button id="close-math-review-modal" class="math-review-close-modal-button">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="math-review-modal-body">
                    ${createReviewScoreOverviewSection(score, level, isQualified, hasScoreOverride, hasLevelOverride, formattedDate, formattedTime)}
                    ${createDebugDataSection()}
                    ${createReviewLevelBreakdownSection()}
                    ${createReviewTopicAnalysisSection()}
                    ${createReviewQuestionAnalysisSection()}
                    ${createReviewInteractionDataSection()}
                    ${createReviewPerformanceAnalysisSection(feedback)}
                    ${createReviewStrengthsImprovementsSection(strengths, improvements)}
                    ${createReviewAdminToolsSection()}
                    ${createReviewManualOverrideSection(originalScore, originalLevel)}
                </div>
            </div>
        `;
    }

    /**
     * Create error content when data is not available
     */
    function createErrorContent(userName) {
        return `
            <div class="math-review-modal-content">
                <div class="math-review-modal-header">
                    <div class="math-review-modal-title-container">
                        <h2 class="math-review-modal-employee-title">${userName}</h2>
                        <h3 class="math-review-modal-subtitle">Mathematics Assessment Review</h3>
                    </div>
                    <div class="math-review-modal-actions">
                        <button id="close-math-review-modal" class="math-review-close-modal-button">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="math-review-modal-body">
                    <div class="math-review-error-content">
                        <div class="math-review-error-icon">
                            <svg width="48" height="48" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                            </svg>
                        </div>
                        <h3>Assessment Data Unavailable</h3>
                        <p>Unable to load mathematics assessment data for review. Please try again later or contact support if the problem persists.</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create review score overview section
     */
    function createReviewScoreOverviewSection(score, level, isQualified, hasScoreOverride, hasLevelOverride, formattedDate, formattedTime) {
        return `
            <!-- Score Overview Section -->
            <div class="math-review-score-overview ${isQualified ? 'qualified' : 'needs-improvement'}">
                <div class="math-review-score-display">
                    <div class="math-review-score-value ${hasScoreOverride || hasLevelOverride ? 'overridden' : ''}">${score}/100</div>
                    <div class="math-review-score-level ${hasScoreOverride || hasLevelOverride ? 'overridden' : ''}">${level} Level</div>
                    ${hasScoreOverride ? '<div class="override-indicator">Score Override Applied</div>' : ''}
                    ${hasLevelOverride && !hasScoreOverride ? '<div class="override-indicator">Level Override Applied</div>' : ''}
                    <div class="math-review-score-status ${isQualified ? 'qualified' : 'needs-improvement'}">
                        ${isQualified ? 'Qualified for Advanced Mathematics Training' : 'Additional Mathematics Support Recommended'}
                    </div>
                </div>
                <div class="math-review-assessment-meta">
                    <div class="math-review-meta-item">
                        <span class="math-review-meta-label">Completed:</span>
                        <span class="math-review-meta-value">${formattedDate}</span>
                    </div>
                    <div class="math-review-meta-item">
                        <span class="math-review-meta-label">Time Spent:</span>
                        <span class="math-review-meta-value">${formattedTime}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create review performance analysis section
     */
    function createReviewPerformanceAnalysisSection(feedback) {
        return `
            <!-- Performance Analysis Section -->
            <div class="math-review-analysis-section">
                <h3>Performance Analysis</h3>
                <div class="math-review-feedback-grid">
                    <div class="math-review-feedback-item">
                        <h4>Numerical Skills</h4>
                        <p>${feedback.numericalSkills || 'Assessment completed'}</p>
                    </div>
                    <div class="math-review-feedback-item">
                        <h4>Algebraic Thinking</h4>
                        <p>${feedback.algebraicThinking || 'Algebraic reasoning evaluated'}</p>
                    </div>
                    <div class="math-review-feedback-item">
                        <h4>Problem Solving</h4>
                        <p>${feedback.problemSolving || 'Problem-solving assessed'}</p>
                    </div>
                    <div class="math-review-feedback-item">
                        <h4>Geometric Reasoning</h4>
                        <p>${feedback.geometricReasoning || 'Geometric understanding evaluated'}</p>
                    </div>
                    <div class="math-review-feedback-item">
                        <h4>Data Handling</h4>
                        <p>${feedback.dataHandling || 'Data interpretation assessed'}</p>
                    </div>
                </div>
                <div class="math-review-overall-feedback">
                    <h4>Overall Assessment</h4>
                    <p>${feedback.overall || 'Mathematics assessment completed successfully'}</p>
                </div>
            </div>
        `;
    }

    /**
     * Create debug data section (temporary for troubleshooting)
     */
    function createDebugDataSection() {
        if (!currentReviewData) return '';

        // Only show in development environment
        if (window.location.hostname !== 'localhost' && !window.location.hostname.includes('127.0.0.1')) {
            return '';
        }

        return `
            <!-- Debug Data Section (Development Only) -->
            <div class="math-debug-section" style="margin-bottom: 1.5rem; border: 2px dashed #f59e0b; padding: 1rem; background: #fffbeb;">
                <h3 style="font-size: 0.9rem; font-weight: 600; color: #b45309; margin-bottom: 0.75rem;">Debug Data Structure</h3>
                <details>
                    <summary style="cursor: pointer; font-weight: 500; color: #b45309;">Click to view raw data structure</summary>
                    <pre style="background: #fff; padding: 1rem; border-radius: 4px; overflow: auto; max-height: 300px; font-size: 0.7rem; margin-top: 0.5rem;">${JSON.stringify(currentReviewData, null, 2)}</pre>
                </details>
                <div style="margin-top: 0.5rem; font-size: 0.8rem; color: #b45309;">
                    <p>This section is only visible in development environments.</p>
                </div>
            </div>
        `;
    }

    /**
     * Create review topic analysis section
     */
    function createReviewTopicAnalysisSection() {
        if (!currentReviewData) return '';

        const topicData = [];
        const levels = ['mathEntryLevel', 'mathLevel1', 'mathGCSEPart1', 'mathGCSEPart2'];

        levels.forEach(levelKey => {
            const levelData = currentReviewData[levelKey];
            if (levelData?.completed && levelData.topicBreakdown) {
                if (levelData.topicBreakdown && typeof levelData.topicBreakdown === 'object') {
                    // Log the topic breakdown structure for debugging
                    console.log(`Topic breakdown for ${levelKey}:`, levelData.topicBreakdown);

                    Object.entries(levelData.topicBreakdown).forEach(([topic, data]) => {
                        // Handle different possible data structures
                        let score = 0;
                        let maxScore = 0;

                        // Case 1: { score: X, maxScore: Y }
                        if (data && typeof data === 'object' && 'score' in data && 'maxScore' in data) {
                            score = data.score || 0;
                            maxScore = data.maxScore || 0;
                        }
                        // Case 2: { correct: X, total: Y }
                        else if (data && typeof data === 'object' && 'correct' in data && 'total' in data) {
                            score = data.correct || 0;
                            maxScore = data.total || 0;
                        }
                        // Case 3: Just a number (score)
                        else if (typeof data === 'number') {
                            score = data;
                            // Use default max scores based on level and topic
                            const defaultMaxScores = {
                                'Entry': { arithmetic: 8, fractions: 6, percentages: 4, basicAlgebra: 6, measurement: 4, dataHandling: 4 },
                                'Level1': { advancedArithmetic: 4, fractionsDecimals: 4, percentagesRatio: 4, algebraicExpressions: 6, geometry: 4, statistics: 4 },
                                'GCSEPart1': { numberOperations: 3, algebraicManipulation: 3, geometricReasoning: 2, fractionalCalculations: 2 },
                                'GCSEPart2': { complexCalculations: 4, statisticalAnalysis: 4, trigonometry: 4, advancedAlgebra: 4, problemSolving: 4 }
                            };

                            const levelKey = levelKey.replace('math', '');
                            maxScore = defaultMaxScores[levelKey] && defaultMaxScores[levelKey][topic] ?
                                defaultMaxScores[levelKey][topic] : 5; // Default to 5 if unknown
                        }

                        const percentage = maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
                        const status = maxScore > 0 ?
                            (score / maxScore >= 0.7 ? 'excellent' :
                             score / maxScore >= 0.5 ? 'good' : 'needs-work') : 'no-data';

                        topicData.push({
                            level: levelKey.replace('math', ''),
                            topic: formatTopicName(topic),
                            score: score,
                            maxScore: maxScore,
                            percentage: percentage,
                            status: status
                        });
                    });
                }
            }
        });

        if (topicData.length === 0) {
            return `
                <div class="math-review-topic-analysis">
                    <h3>Topic Performance Analysis</h3>
                    <p class="no-topic-data">No topic performance data available for detailed analysis.</p>
                </div>
            `;
        }

        // Group topics by performance level for admin insights
        const excellentTopics = topicData.filter(t => t.status === 'excellent');
        const goodTopics = topicData.filter(t => t.status === 'good');
        const needsWorkTopics = topicData.filter(t => t.status === 'needs-work');

        return `
            <!-- Topic Analysis Section -->
            <div class="math-review-topic-analysis">
                <h3>Topic Performance Analysis</h3>

                <div class="topic-performance-summary">
                    <div class="performance-stat excellent">
                        <span class="stat-number">${excellentTopics.length}</span>
                        <span class="stat-label">Excellent Topics (70%+)</span>
                    </div>
                    <div class="performance-stat good">
                        <span class="stat-number">${goodTopics.length}</span>
                        <span class="stat-label">Good Topics (50-69%)</span>
                    </div>
                    <div class="performance-stat needs-work">
                        <span class="stat-number">${needsWorkTopics.length}</span>
                        <span class="stat-label">Needs Work (<50%)</span>
                    </div>
                </div>

                <div class="detailed-topic-breakdown">
                    ${topicData.map(topic => {
                        // Ensure all values are properly formatted
                        const score = typeof topic.score === 'number' ? topic.score : 0;
                        const maxScore = typeof topic.maxScore === 'number' ? topic.maxScore : 0;
                        const percentage = typeof topic.percentage === 'number' ? topic.percentage : 0;
                        const status = topic.status || 'no-data';

                        return `
                        <div class="topic-detail-item ${status}">
                            <div class="topic-detail-header">
                                <span class="topic-detail-name">${topic.topic}</span>
                                <span class="topic-detail-level">${topic.level}</span>
                                <span class="topic-detail-score">${score}/${maxScore} (${percentage}%)</span>
                            </div>
                            <div class="topic-detail-progress">
                                <div class="topic-detail-bar">
                                    <div class="topic-detail-fill ${status}" style="width: ${percentage}%"></div>
                                </div>
                            </div>
                        </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    }

    /**
     * Create review question analysis section
     */
    function createReviewQuestionAnalysisSection() {
        // Check for question responses in multiple possible locations
        let responses = [];

        if (currentReviewData?.mathAssessmentResponses?.questionResponses) {
            responses = currentReviewData.mathAssessmentResponses.questionResponses;
        } else if (currentReviewData?.responses) {
            responses = currentReviewData.responses;
        } else {
            // Try to extract responses from level data
            const levels = ['mathEntryLevel', 'mathLevel1', 'mathGCSEPart1', 'mathGCSEPart2'];
            levels.forEach(levelKey => {
                const levelData = currentReviewData[levelKey];
                if (levelData?.responses && Array.isArray(levelData.responses)) {
                    responses = responses.concat(levelData.responses);
                }
            });
        }

        if (!responses || responses.length === 0) {
            return `
                <div class="math-review-question-analysis">
                    <h3>Question-by-Question Analysis</h3>
                    <div class="no-question-data">
                        <p>No detailed question response data available for analysis.</p>
                        <p class="data-note">This may be because:</p>
                        <ul class="data-reasons">
                            <li>The assessment was completed before detailed logging was implemented</li>
                            <li>The student did not complete the full assessment</li>
                            <li>Response data was not properly recorded</li>
                        </ul>
                    </div>
                </div>
            `;
        }

        const totalQuestions = responses.length;
        const correctAnswers = responses.filter(r => r.isCorrect || r.correct).length;
        const incorrectAnswers = totalQuestions - correctAnswers;
        const averageTime = responses.length > 0 ?
            Math.round(responses.reduce((sum, r) => sum + (r.timeSpent || r.time || 0), 0) / responses.length / 1000) : 0;

        // Group questions by level and topic for better analysis
        const questionsByLevel = {};
        responses.forEach(response => {
            const level = response.level || 'Unknown';
            if (!questionsByLevel[level]) {
                questionsByLevel[level] = [];
            }
            questionsByLevel[level].push(response);
        });

        return `
            <!-- Question Analysis Section -->
            <div class="math-review-question-analysis">
                <h3>Question-by-Question Analysis</h3>

                <div class="question-summary-stats">
                    <div class="question-stat">
                        <span class="stat-number">${totalQuestions}</span>
                        <span class="stat-label">Total Questions</span>
                    </div>
                    <div class="question-stat correct">
                        <span class="stat-number">${correctAnswers}</span>
                        <span class="stat-label">Correct</span>
                    </div>
                    <div class="question-stat incorrect">
                        <span class="stat-number">${incorrectAnswers}</span>
                        <span class="stat-label">Incorrect</span>
                    </div>
                    <div class="question-stat">
                        <span class="stat-number">${averageTime}s</span>
                        <span class="stat-label">Avg. Time</span>
                    </div>
                </div>

                <div class="questions-by-level">
                    ${Object.entries(questionsByLevel).map(([level, questions]) => `
                        <div class="level-questions-group">
                            <h4>${level} Questions (${questions.length})</h4>
                            <div class="questions-grid">
                                ${questions.map((q, index) => {
                                    // Handle different data structures for correctness
                                    const isCorrect = q.isCorrect || q.correct || false;
                                    const questionText = q.questionText || q.question || 'Question text not available';
                                    const studentAnswer = q.studentAnswer || q.answer || q.userAnswer || 'No answer provided';
                                    const correctAnswer = q.correctAnswer || q.solution || 'Not available';
                                    const timeSpent = q.timeSpent || q.time || 0;
                                    const topic = q.topic || q.category || 'General';

                                    return `
                                    <div class="question-item ${isCorrect ? 'correct' : 'incorrect'}">
                                        <div class="question-header">
                                            <span class="question-number">Q${index + 1}</span>
                                            <span class="question-topic">${topic}</span>
                                            <span class="question-result ${isCorrect ? 'correct' : 'incorrect'}">
                                                ${isCorrect ? '✓' : '✗'}
                                            </span>
                                        </div>
                                        <div class="question-details">
                                            <div class="question-text">${questionText}</div>
                                            <div class="question-answers">
                                                <div class="student-answer">
                                                    <strong>Student:</strong> ${studentAnswer}
                                                </div>
                                                <div class="correct-answer">
                                                    <strong>Correct:</strong> ${correctAnswer}
                                                </div>
                                            </div>
                                            <div class="question-meta">
                                                <span class="question-time">Time: ${Math.round(timeSpent / 1000)}s</span>
                                                ${q.difficulty ? `<span class="question-difficulty">Difficulty: ${q.difficulty}</span>` : ''}
                                            </div>
                                        </div>
                                    </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    /**
     * Create review interaction data section
     */
    function createReviewInteractionDataSection() {
        const assessmentData = currentReviewData?.mathAssessmentResponses;
        if (!assessmentData) {
            return `
                <div class="math-review-interaction-data">
                    <h3>Assessment Interaction Data</h3>
                    <p class="no-interaction-data">No interaction data available for analysis.</p>
                </div>
            `;
        }

        const metadata = assessmentData.assessmentMetadata || {};
        const interactionLog = assessmentData.interactionLog || [];

        return `
            <!-- Interaction Data Section -->
            <div class="math-review-interaction-data">
                <h3>Assessment Interaction Data</h3>

                <div class="interaction-metadata">
                    <h4>Session Information</h4>
                    <div class="metadata-grid">
                        <div class="metadata-item">
                            <span class="metadata-label">Session ID:</span>
                            <span class="metadata-value">${metadata.sessionId || 'Not recorded'}</span>
                        </div>
                        <div class="metadata-item">
                            <span class="metadata-label">Start Time:</span>
                            <span class="metadata-value">${metadata.startTime ? new Date(metadata.startTime).toLocaleString() : 'Not recorded'}</span>
                        </div>
                        <div class="metadata-item">
                            <span class="metadata-label">End Time:</span>
                            <span class="metadata-value">${metadata.endTime ? new Date(metadata.endTime).toLocaleString() : 'Not recorded'}</span>
                        </div>
                        <div class="metadata-item">
                            <span class="metadata-label">Total Interactions:</span>
                            <span class="metadata-value">${metadata.totalInteractions || 0}</span>
                        </div>
                        <div class="metadata-item">
                            <span class="metadata-label">Calculator Used:</span>
                            <span class="metadata-value">${metadata.calculatorUsed ? 'Yes' : 'No'}</span>
                        </div>
                        <div class="metadata-item">
                            <span class="metadata-label">Assistive Technology:</span>
                            <span class="metadata-value">${metadata.assistiveTechnologyUsed ? 'Yes' : 'No'}</span>
                        </div>
                    </div>
                </div>

                ${metadata.browserInfo && Object.keys(metadata.browserInfo).length > 0 ? `
                    <div class="browser-information">
                        <h4>Browser Information</h4>
                        <div class="browser-details">
                            ${Object.entries(metadata.browserInfo).map(([key, value]) => `
                                <div class="browser-item">
                                    <span class="browser-label">${key}:</span>
                                    <span class="browser-value">${value}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}

                ${interactionLog.length > 0 ? `
                    <div class="interaction-log">
                        <h4>Interaction Timeline (Last 10 events)</h4>
                        <div class="interaction-events">
                            ${interactionLog.slice(-10).map(event => `
                                <div class="interaction-event">
                                    <span class="event-time">${new Date(event.timestamp).toLocaleTimeString()}</span>
                                    <span class="event-type">${event.eventType}</span>
                                    <span class="event-details">${event.details || ''}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * Create review level breakdown section
     */
    function createReviewLevelBreakdownSection() {
        if (!currentReviewData) return '';

        const levels = ['mathEntryLevel', 'mathLevel1', 'mathGCSEPart1', 'mathGCSEPart2'];
        const levelNames = {
            'mathEntryLevel': 'Entry Level',
            'mathLevel1': 'Level 1',
            'mathGCSEPart1': 'GCSE Part 1',
            'mathGCSEPart2': 'GCSE Part 2'
        };

        const completedLevels = levels.filter(level => currentReviewData[level] && currentReviewData[level].completed);

        if (completedLevels.length === 0) {
            return `
                <div class="math-review-level-breakdown">
                    <h3>Level Assessment Breakdown</h3>
                    <p>No level assessments completed.</p>
                </div>
            `;
        }

        return `
            <div class="math-review-level-breakdown">
                <h3>Level Assessment Breakdown</h3>
                <div class="math-review-levels-grid">
                    ${completedLevels.map(level => {
                        const levelData = currentReviewData[level];
                        const passed = levelData.passed ? 'passed' : 'failed';

                        return `
                            <div class="math-review-level-card ${passed}">
                                <div class="math-review-level-header">
                                    <h4>${levelNames[level]}</h4>
                                    <span class="math-review-level-status ${passed}">${passed.toUpperCase()}</span>
                                </div>
                                <div class="math-review-level-details">
                                    <div class="math-review-level-score">Score: ${levelData.score}/${getMaxScore(level.replace('math', '').toLowerCase())}</div>
                                    <div class="math-review-level-time">Time: ${formatDuration(levelData.timeSpent)}</div>
                                    <div class="math-review-level-date">Completed: ${formatDate(levelData.completedAt)}</div>
                                </div>
                                ${levelData.topicBreakdown ? `
                                    <div class="math-review-topic-breakdown">
                                        <h5>Topic Scores:</h5>
                                        ${Object.entries(levelData.topicBreakdown).map(([topic, scoreData]) => {
                                            const score = scoreData?.score || 0;
                                            const maxScore = scoreData?.maxScore || 0;
                                            const percentage = maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
                                            return `<div class="math-review-topic-score">${formatTopicName(topic)}: ${score}/${maxScore} (${percentage}%)</div>`;
                                        }).join('')}
                                    </div>
                                ` : ''}
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    }

    /**
     * Create review strengths and improvements section
     */
    function createReviewStrengthsImprovementsSection(strengths, improvements) {
        return `
            <!-- Strengths and Improvements Section -->
            <div class="math-review-strengths-improvements">
                <div class="math-review-strengths-section">
                    <h4>Identified Strengths</h4>
                    <ul class="math-review-strengths-list">
                        ${strengths.map(strength => `<li>${strength}</li>`).join('')}
                    </ul>
                </div>
                <div class="math-review-improvements-section">
                    <h4>Areas for Development</h4>
                    <ul class="math-review-improvements-list">
                        ${improvements.map(improvement => `<li>${improvement}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;
    }

    /**
     * Create assessment summary section (legacy support)
     */
    function createAssessmentSummary() {
        const hasScoreOverride = currentMathData.manualScoreOverride !== undefined;
        const hasLevelOverride = currentMathData.manualLevelOverride !== undefined;
        const displayScore = hasScoreOverride ? currentMathData.manualScoreOverride : currentMathData.overallScore;
        const displayLevel = hasLevelOverride ? currentMathData.manualLevelOverride : 
                           (currentMathData.placementRecommendation?.level || currentMathData.currentLevel);

        return `
            <div class="math-review-summary">
                <div class="math-review-score-display">
                    <div class="math-review-score-value ${hasScoreOverride ? 'overridden' : ''}">${displayScore} points</div>
                    <div class="math-review-level-display">
                        <span class="math-review-level ${hasLevelOverride ? 'overridden' : ''}">${displayLevel}</span>
                        ${hasScoreOverride ? '<span class="override-indicator">Score Override Applied</span>' : ''}
                        ${hasLevelOverride && !hasScoreOverride ? '<span class="override-indicator">Level Override Applied</span>' : ''}
                    </div>
                </div>
                <div class="math-review-score-override">
                    <label for="math-score-override-input">Override Score:</label>
                    <input type="number" id="math-score-override-input" min="0" max="100" 
                           placeholder="Enter score" value="${hasScoreOverride ? currentMathData.manualScoreOverride : ''}">
                    <button id="math-apply-override-btn" class="math-override-btn">Apply Override</button>
                </div>
                <div class="math-review-metadata">
                    <div class="math-metadata-item">
                        <span class="math-metadata-label">Highest Level:</span>
                        <span class="math-metadata-value">${currentMathData.highestLevelCompleted || 'Entry'}</span>
                    </div>
                    <div class="math-metadata-item">
                        <span class="math-metadata-label">Time Spent:</span>
                        <span class="math-metadata-value">${formatDuration(currentMathData.totalTimeSpent)}</span>
                    </div>
                    <div class="math-metadata-item">
                        <span class="math-metadata-label">Completed:</span>
                        <span class="math-metadata-value">${formatDate(currentMathData.timestamp)}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create detailed analysis section
     */
    function createDetailedAnalysis() {
        if (!currentMathData.feedback) {
            return '<div class="math-analysis-section"><p>No detailed analysis available.</p></div>';
        }

        const feedback = currentMathData.feedback;
        const strengths = currentMathData.strengths || [];
        const improvements = currentMathData.improvements || [];

        return `
            <div class="math-analysis-section">
                <h3>Detailed Mathematics Analysis</h3>

                <div class="math-feedback-grid">
                    <div class="math-feedback-item">
                        <h4>Numerical Skills</h4>
                        <p>${feedback.numericalSkills || 'Not assessed'}</p>
                    </div>
                    <div class="math-feedback-item">
                        <h4>Algebraic Thinking</h4>
                        <p>${feedback.algebraicThinking || 'Not assessed'}</p>
                    </div>
                    <div class="math-feedback-item">
                        <h4>Problem Solving</h4>
                        <p>${feedback.problemSolving || 'Not assessed'}</p>
                    </div>
                    <div class="math-feedback-item">
                        <h4>Geometric Reasoning</h4>
                        <p>${feedback.geometricReasoning || 'Not assessed'}</p>
                    </div>
                    <div class="math-feedback-item">
                        <h4>Data Handling</h4>
                        <p>${feedback.dataHandling || 'Not assessed'}</p>
                    </div>
                    <div class="math-feedback-item math-feedback-overall">
                        <h4>Overall Assessment</h4>
                        <p>${feedback.overall || 'Not assessed'}</p>
                    </div>
                </div>

                <div class="math-strengths-improvements">
                    <div class="math-strengths">
                        <h4>Identified Strengths</h4>
                        <ul>
                            ${strengths.map(strength => `<li>${strength}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="math-improvements">
                        <h4>Areas for Development</h4>
                        <ul>
                            ${improvements.map(improvement => `<li>${improvement}</li>`).join('')}
                        </ul>
                    </div>
                </div>

                ${currentMathData.placementRecommendation ? `
                    <div class="math-placement-recommendation">
                        <h4>Placement Recommendation</h4>
                        <div class="math-placement-level">${currentMathData.placementRecommendation.level}</div>
                        <p>${currentMathData.placementRecommendation.reasoning || ''}</p>
                        ${currentMathData.placementRecommendation.nextSteps ? `
                            <div class="math-next-steps">
                                <h5>Recommended Next Steps:</h5>
                                <ul>
                                    ${currentMathData.placementRecommendation.nextSteps.map(step => `<li>${step}</li>`).join('')}
                                </ul>
                            </div>
                        ` : ''}
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * Create level breakdown section
     */
    function createLevelBreakdown() {
        const levels = ['entryLevel', 'level1', 'gcsePart1', 'gcsePart2'];
        const levelNames = {
            'entryLevel': 'Entry Level',
            'level1': 'Level 1',
            'gcsePart1': 'GCSE Part 1',
            'gcsePart2': 'GCSE Part 2'
        };

        const completedLevels = levels.filter(level => currentMathData[level] && currentMathData[level].completed);

        if (completedLevels.length === 0) {
            return '<div class="math-level-breakdown"><p>No level assessments completed.</p></div>';
        }

        return `
            <div class="math-level-breakdown">
                <h3>Level Assessment Breakdown</h3>
                <div class="math-levels-grid">
                    ${completedLevels.map(level => {
                        const levelData = currentMathData[level];
                        const passed = levelData.passed ? 'passed' : 'failed';

                        return `
                            <div class="math-level-card ${passed}">
                                <div class="math-level-header">
                                    <h4>${levelNames[level]}</h4>
                                    <span class="math-level-status ${passed}">${passed.toUpperCase()}</span>
                                </div>
                                <div class="math-level-details">
                                    <div class="math-level-score">Score: ${levelData.score}/${getMaxScore(level)}</div>
                                    <div class="math-level-time">Time: ${formatDuration(levelData.timeSpent)}</div>
                                    <div class="math-level-date">Completed: ${formatDate(levelData.completedAt)}</div>
                                </div>
                                ${levelData.topicBreakdown ? `
                                    <div class="math-topic-breakdown">
                                        <h5>Topic Scores:</h5>
                                        ${Object.entries(levelData.topicBreakdown).map(([topic, score]) =>
                                            `<div class="math-topic-score">${formatTopicName(topic)}: ${score}</div>`
                                        ).join('')}
                                    </div>
                                ` : ''}
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    }

    /**
     * Create review admin tools section
     */
    function createReviewAdminToolsSection() {
        const hasDetailedResponses = currentReviewData && currentReviewData.mathAssessmentResponses;

        return `
            <!-- Admin Tools Section -->
            <div class="math-review-admin-tools">
                <h3>Admin Review Tools</h3>
                <div class="math-review-admin-buttons">
                    <button id="view-math-detailed-responses" class="math-admin-review-btn primary">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        View Detailed Responses
                    </button>
                    <button id="export-math-assessment-data" class="math-admin-review-btn secondary">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        Export Assessment Data
                    </button>
                    ${hasDetailedResponses ? `
                        <span class="math-responses-available">✓ Detailed responses available</span>
                    ` : `
                        <span class="math-responses-unavailable">⚠ Limited response data</span>
                    `}
                </div>
                <div class="math-review-admin-info">
                    <p>Use these tools to manually review student responses and override assessment levels if needed.</p>
                </div>
            </div>
        `;
    }

    /**
     * Create review manual override section
     */
    function createReviewManualOverrideSection(originalScore, originalLevel) {
        return `
            <!-- Manual Override Section -->
            <div class="math-review-manual-override">
                <h3>Manual Override</h3>
                <div class="math-override-controls">
                    <div class="math-override-score-section">
                        <h4>Score Override</h4>
                        <div class="math-override-input-group">
                            <label for="math-override-score-input">New Score (0-100):</label>
                            <input type="number" id="math-override-score-input" min="0" max="100"
                                   placeholder="Enter new score" class="math-override-input">
                            <button id="math-apply-score-override" class="math-override-apply-btn">
                                <span class="btn-text">Apply Score Override</span>
                                <span class="btn-loading" style="display: none;">
                                    <svg class="animate-spin" width="16" height="16" fill="none" viewBox="0 0 24 24">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" opacity="0.25"/>
                                        <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
                                    </svg>
                                    Applying...
                                </span>
                            </button>
                        </div>
                        <div class="math-override-explanation">
                            <p>Override the calculated score if manual review indicates a different proficiency level. The system will automatically calculate the appropriate level based on the new score.</p>
                        </div>
                    </div>

                    <div class="math-override-current-values">
                        <h4>Current Values</h4>
                        <div class="math-current-values-grid">
                            <div class="math-current-value-item">
                                <span class="math-current-label">Original Score:</span>
                                <span class="math-current-value">${originalScore}/100</span>
                            </div>
                            <div class="math-current-value-item">
                                <span class="math-current-label">Original Level:</span>
                                <span class="math-current-value">${originalLevel}</span>
                            </div>
                            <div class="math-current-value-item">
                                <span class="math-current-label">Override Status:</span>
                                <span class="math-current-value" id="math-override-status">
                                    ${currentReviewData?.manualScoreOverride ? 'Score Override Active' : 'No Override Applied'}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create admin review section (legacy support)
     */
    function createAdminReviewSection() {
        if (!currentMathData.responses) {
            return `
                <div class="math-admin-review">
                    <h3>Admin Review</h3>
                    <p>No detailed response data available for review.</p>
                </div>
            `;
        }

        return `
            <div class="math-admin-review">
                <h3>Admin Review</h3>
                <div class="math-review-actions">
                    <button id="math-view-responses-btn" class="math-review-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                            <circle cx="12" cy="12" r="3"/>
                        </svg>
                        View Detailed Responses
                    </button>
                    <button id="math-export-data-btn" class="math-review-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                            <polyline points="7,10 12,15 17,10"/>
                            <line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                        Export Assessment Data
                    </button>
                </div>

                <div class="math-response-summary">
                    <h4>Response Summary</h4>
                    <div class="math-summary-stats">
                        <div class="math-stat-item">
                            <span class="math-stat-label">Total Questions:</span>
                            <span class="math-stat-value">${currentMathData.responses.questionResponses?.length || 0}</span>
                        </div>
                        <div class="math-stat-item">
                            <span class="math-stat-label">Correct Answers:</span>
                            <span class="math-stat-value">${getCorrectAnswersCount()}</span>
                        </div>
                        <div class="math-stat-item">
                            <span class="math-stat-label">Total Interactions:</span>
                            <span class="math-stat-value">${currentMathData.responses.assessmentMetadata?.totalInteractions || 0}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Helper functions
     */
    function formatDuration(seconds) {
        if (!seconds) return '0 minutes';
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        if (minutes === 0) return `${remainingSeconds} seconds`;
        if (remainingSeconds === 0) return `${minutes} minutes`;
        return `${minutes}m ${remainingSeconds}s`;
    }

    function formatDate(timestamp) {
        if (!timestamp) return 'Not available';
        const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }

    function getMaxScore(level) {
        const maxScores = {
            'entryLevel': 44,
            'level1': 26,
            'gcsePart1': 10,
            'gcsePart2': 20
        };
        return maxScores[level] || 0;
    }

    function formatTopicName(topic) {
        const topicNames = {
            'arithmetic': 'Arithmetic',
            'fractions': 'Fractions',
            'percentages': 'Percentages',
            'basicAlgebra': 'Basic Algebra',
            'measurement': 'Measurement',
            'dataHandling': 'Data Handling',
            'advancedArithmetic': 'Advanced Arithmetic',
            'fractionsDecimals': 'Fractions & Decimals',
            'percentagesRatio': 'Percentages & Ratio',
            'algebraicExpressions': 'Algebraic Expressions',
            'geometry': 'Geometry',
            'statistics': 'Statistics',
            'numberOperations': 'Number Operations',
            'algebraicManipulation': 'Algebraic Manipulation',
            'geometricReasoning': 'Geometric Reasoning',
            'fractionalCalculations': 'Fractional Calculations',
            'complexCalculations': 'Complex Calculations',
            'statisticalAnalysis': 'Statistical Analysis',
            'trigonometry': 'Trigonometry',
            'advancedAlgebra': 'Advanced Algebra',
            'problemSolving': 'Problem Solving'
        };

        return topicNames[topic] || topic.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    }

    function getCorrectAnswersCount() {
        if (!currentMathData.responses?.questionResponses) return 0;
        return currentMathData.responses.questionResponses.filter(q => q.isCorrect).length;
    }

    /**
     * Initialize event listeners
     */
    function initializeEventListeners(overlay) {
        // Close modal on overlay click
        overlay.addEventListener('click', overlayClickHandler);

        // Score override functionality
        const overrideBtn = overlay.querySelector('#math-apply-override-btn');
        if (overrideBtn) {
            overrideBtn.addEventListener('click', handleScoreOverride);
        }

        // View responses functionality
        const viewResponsesBtn = overlay.querySelector('#math-view-responses-btn');
        if (viewResponsesBtn) {
            viewResponsesBtn.addEventListener('click', handleViewResponses);
        }

        // Export data functionality
        const exportBtn = overlay.querySelector('#math-export-data-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', handleExportData);
        }
    }

    /**
     * Handle score override
     */
    async function handleScoreOverride() {
        const input = document.getElementById('math-score-override-input');
        const btn = document.getElementById('math-apply-override-btn');

        if (!input || !btn) return;

        const newScore = parseInt(input.value);
        if (isNaN(newScore) || newScore < 0 || newScore > 100) {
            if (typeof showNotification === 'function') {
                showNotification('Please enter a valid score between 0 and 100', 'error');
            }
            return;
        }

        // Show loading state
        const originalText = btn.textContent;
        btn.textContent = 'Applying...';
        btn.disabled = true;

        try {
            // Update Firebase
            const userRef = db.collection('companies')
                             .doc(currentUserCompany)
                             .collection('users')
                             .doc(currentUserEmail);

            await userRef.update({
                manualScoreOverride: newScore,
                manualLevelOverride: determineLevelFromScore(newScore),
                updatedAt: firebase.firestore.FieldValue.serverTimestamp()
            });

            // Update local data
            currentMathData.manualScoreOverride = newScore;
            currentMathData.manualLevelOverride = determineLevelFromScore(newScore);

            // Update UI
            updateScoreDisplay(newScore, true);

            if (typeof showNotification === 'function') {
                showNotification('Score override applied successfully', 'success');
            }

        } catch (error) {
            console.error('Error applying score override:', error);
            if (typeof showNotification === 'function') {
                showNotification('Failed to apply score override', 'error');
            }
        } finally {
            btn.textContent = originalText;
            btn.disabled = false;
        }
    }

    /**
     * Handle view responses
     */
    function handleViewResponses() {
        if (!currentMathData.responses?.questionResponses) {
            if (typeof showNotification === 'function') {
                showNotification('No detailed response data available', 'error');
            }
            return;
        }

        // Create detailed response view
        const responses = currentMathData.responses.questionResponses;
        let reviewText = `Mathematics Assessment Review - ${currentUserName}\n`;
        reviewText += `Email: ${currentUserEmail}\n`;
        reviewText += `Company: ${currentUserCompany}\n`;
        reviewText += `Assessment Date: ${formatDate(currentMathData.timestamp)}\n\n`;

        responses.forEach((response, index) => {
            reviewText += `Question ${index + 1}:\n`;
            reviewText += `Type: ${response.questionType || 'Unknown'}\n`;
            reviewText += `Topic: ${response.topic || 'Unknown'}\n`;
            reviewText += `Question: ${response.questionText || 'Not available'}\n`;
            reviewText += `Correct Answer: ${response.correctAnswer || 'Not available'}\n`;
            reviewText += `Student Answer: ${response.studentAnswer || 'No response'}\n`;
            reviewText += `Result: ${response.isCorrect ? 'CORRECT' : 'INCORRECT'}\n`;
            reviewText += `Time Spent: ${response.timeSpent || 0}ms\n\n`;
        });

        // Show in a new window
        const reviewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
        reviewWindow.document.write(`
            <html>
                <head>
                    <title>Mathematics Assessment Review</title>
                    <style>
                        body { font-family: monospace; padding: 20px; line-height: 1.6; }
                        h1 { color: #333; }
                        .correct { color: green; }
                        .incorrect { color: red; }
                    </style>
                </head>
                <body>
                    <h1>Mathematics Assessment Review</h1>
                    <pre>${reviewText}</pre>
                    <button onclick="window.close()">Close</button>
                </body>
            </html>
        `);
    }

    /**
     * Handle export data
     */
    function handleExportData() {
        const exportData = {
            userInfo: {
                name: currentUserName,
                email: currentUserEmail,
                company: currentUserCompany
            },
            assessment: currentMathData,
            exportDate: new Date().toISOString()
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `math-assessment-${currentUserEmail}-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        URL.revokeObjectURL(url);
    }

    /**
     * Helper functions continued
     */
    function determineLevelFromScore(score) {
        if (score >= 80) return 'GCSE Higher';
        if (score >= 60) return 'GCSE Foundation';
        if (score >= 40) return 'Level 1';
        return 'Entry Support';
    }

    function updateScoreDisplay(newScore, isOverride) {
        const scoreValue = document.querySelector('.math-review-score-value');
        const levelDisplay = document.querySelector('.math-review-level');

        if (scoreValue) {
            scoreValue.textContent = `${newScore} points`;
            scoreValue.className = isOverride ? 'math-review-score-value overridden' : 'math-review-score-value';
        }

        if (levelDisplay) {
            const newLevel = determineLevelFromScore(newScore);
            levelDisplay.textContent = newLevel;
            levelDisplay.className = isOverride ? 'math-review-level overridden' : 'math-review-level';
        }

        // Update override indicator
        const existingIndicator = document.querySelector('.override-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        if (isOverride) {
            const scoreDisplay = document.querySelector('.math-review-score-display');
            if (scoreDisplay) {
                const indicator = document.createElement('span');
                indicator.className = 'override-indicator';
                indicator.textContent = 'Score Override Applied';
                scoreDisplay.appendChild(indicator);
            }
        }
    }

    /**
     * Handle overlay click to close modal
     */
    function overlayClickHandler(event) {
        if (event.target.id === 'math-review-overlay') {
            hide();
        }
    }

    /**
     * Add review modal styles matching English assessment review modal
     */
    function addReviewModalStyles() {
        if (document.getElementById('math-review-modal-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'math-review-modal-styles';
        styles.textContent = `
            /* Modal Overlay - matches English review modal */
            .math-review-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.6);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                padding: 1rem;
                opacity: 0;
                transition: opacity 0.4s ease;
            }

            /* Modal Content - matches English review modal */
            .math-review-modal-content {
                background: white;
                border-radius: 8px;
                width: 100%;
                max-width: 900px;
                max-height: 90vh;
                overflow-y: auto;
                opacity: 0;
                transform: scale(0.95);
                transition: all 0.4s ease;
                font-family: sans-serif;
                color: #374151;
                position: relative;
                font-size: 0.875rem;
                box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            }

            /* Modal Header - matches English review modal */
            .math-review-modal-header {
                padding: 0.75rem;
                border-bottom: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
            }

            .math-review-modal-title-container {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;
            }

            .math-review-modal-employee-title {
                font-size: 1rem;
                font-weight: 600;
                color: #1e3a8a;
                margin: 0;
            }

            .math-review-modal-subtitle {
                font-size: 0.8rem;
                font-weight: 500;
                color: #4b5563;
                margin: 0;
            }

            .math-review-modal-actions {
                display: flex;
                gap: 0.5rem;
            }

            .math-review-close-modal-button {
                background: none;
                border: 1px solid #1e3a8a;
                border-radius: 4px;
                padding: 0.25rem 0.5rem;
                cursor: pointer;
                color: #1e3a8a;
            }

            .math-review-close-modal-button svg {
                stroke: #1e3a8a;
            }

            .math-review-close-modal-button:hover {
                background: #1e3a8a;
                color: #fff;
            }

            .math-review-close-modal-button:hover svg {
                stroke: #fff;
            }

            /* Modal Body - matches English review modal */
            .math-review-modal-body {
                padding: 1rem;
                line-height: 1.4;
            }

            /* Score Overview Section - matches English review modal */
            .math-review-score-overview {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #f9fafb;
                margin-bottom: 1.5rem;
                border-left: 4px solid #1e3a8a;
            }

            .math-review-score-overview.qualified {
                border-left-color: #059669;
            }

            .math-review-score-overview.needs-improvement {
                border-left-color: #f59e0b;
            }

            .math-review-score-display {
                text-align: center;
                margin-bottom: 1rem;
            }

            .math-review-score-value {
                font-size: 2rem;
                font-weight: 600;
                color: #1e3a8a;
                line-height: 1;
            }

            .math-review-score-value.overridden {
                color: #dc2626;
                position: relative;
            }

            .math-review-score-level {
                font-size: 0.9rem;
                font-weight: 500;
                color: #4b5563;
                margin: 0.5rem 0;
            }

            .math-review-score-level.overridden {
                color: #dc2626;
            }

            .override-indicator {
                font-size: 0.75rem;
                color: #dc2626;
                font-weight: 500;
                background: #fef2f2;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                margin: 0.25rem 0;
                border: 1px solid #fecaca;
                display: inline-block;
            }

            .math-review-score-status {
                font-size: 0.8rem;
                font-weight: 500;
                padding: 0.25rem 0.5rem;
                border-radius: 9999px;
                display: inline-block;
                background: #f0f9ff;
                color: #1e3a8a;
            }

            .math-review-score-status.qualified {
                background: #f0fdf4;
                color: #059669;
            }

            .math-review-score-status.needs-improvement {
                background: #fffbeb;
                color: #f59e0b;
            }

            .math-review-assessment-meta {
                display: flex;
                justify-content: center;
                gap: 1rem;
                margin-top: 1rem;
                font-size: 0.8rem;
            }

            .math-review-meta-item {
                text-align: center;
            }

            .math-review-meta-label {
                display: block;
                color: #6b7280;
                margin-bottom: 0.25rem;
            }

            .math-review-meta-value {
                font-weight: 600;
                color: #374151;
            }

            /* Analysis Section - matches English review modal */
            .math-review-analysis-section {
                margin-bottom: 1.5rem;
            }

            .math-review-analysis-section h3 {
                font-size: 0.9rem;
                font-weight: 600;
                color: #1e3a8a;
                margin-bottom: 0.75rem;
            }

            .math-review-feedback-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 0.75rem;
                margin-bottom: 1rem;
            }

            .math-review-feedback-item {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #ffffff;
            }

            .math-review-feedback-item h4 {
                margin: 0 0 0.5rem;
                font-size: 0.9rem;
                color: #1e3a8a;
            }

            .math-review-feedback-item p {
                margin: 0;
                color: #4b5563;
                line-height: 1.4;
            }

            .math-review-overall-feedback {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #f8fafc;
                border-left: 3px solid #1e3a8a;
            }

            .math-review-overall-feedback h4 {
                margin: 0 0 0.5rem;
                font-size: 0.9rem;
                color: #1e3a8a;
            }

            .math-review-overall-feedback p {
                margin: 0;
                color: #4b5563;
                line-height: 1.4;
            }

            /* Level Breakdown Section - matches English review modal */
            .math-review-level-breakdown {
                margin-bottom: 1.5rem;
            }

            .math-review-level-breakdown h3 {
                font-size: 0.9rem;
                font-weight: 600;
                color: #1e3a8a;
                margin-bottom: 0.75rem;
            }

            .math-review-levels-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 1rem;
            }

            .math-review-level-card {
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 1rem;
                background: white;
            }

            .math-review-level-card.passed {
                border-color: #22c55e;
                background: #f0fdf4;
            }

            .math-review-level-card.failed {
                border-color: #ef4444;
                background: #fef2f2;
            }

            .math-review-level-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;
            }

            .math-review-level-status.passed {
                background: #22c55e;
                color: white;
                padding: 0.25rem 0.75rem;
                border-radius: 12px;
                font-size: 0.75rem;
                font-weight: 600;
            }

            .math-review-level-status.failed {
                background: #ef4444;
                color: white;
                padding: 0.25rem 0.75rem;
                border-radius: 12px;
                font-size: 0.75rem;
                font-weight: 600;
            }

            /* Strengths and Improvements Section - matches English review modal */
            .math-review-strengths-improvements {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .math-review-strengths-section,
            .math-review-improvements-section {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #ffffff;
            }

            .math-review-strengths-section h4,
            .math-review-improvements-section h4 {
                margin: 0 0 0.5rem;
                font-size: 0.9rem;
                color: #1e3a8a;
            }

            .math-review-strengths-list,
            .math-review-improvements-list {
                margin: 0;
                padding-left: 1rem;
                list-style-type: disc;
            }

            .math-review-strengths-list li,
            .math-review-improvements-list li {
                margin-bottom: 0.25rem;
                color: #4b5563;
                line-height: 1.4;
                font-size: 0.8rem;
            }

            /* Admin Tools Section - matches English review modal */
            .math-review-admin-tools {
                margin-bottom: 1.5rem;
            }

            .math-review-admin-tools h3 {
                font-size: 0.9rem;
                font-weight: 600;
                color: #1e3a8a;
                margin-bottom: 0.75rem;
            }

            .math-review-admin-buttons {
                display: flex;
                gap: 0.5rem;
                margin-bottom: 0.75rem;
                align-items: center;
                flex-wrap: wrap;
            }

            .math-admin-review-btn {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.5rem 0.75rem;
                border-radius: 4px;
                font-size: 0.8rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                border: none;
            }

            .math-admin-review-btn.primary {
                background: #1e3a8a;
                color: white;
            }

            .math-admin-review-btn.primary:hover {
                background: #1e40af;
            }

            .math-admin-review-btn.secondary {
                background: #f3f4f6;
                color: #374151;
                border: 1px solid #d1d5db;
            }

            .math-admin-review-btn.secondary:hover {
                background: #e5e7eb;
            }

            .math-responses-available {
                font-size: 0.75rem;
                color: #059669;
                font-weight: 500;
            }

            .math-responses-unavailable {
                font-size: 0.75rem;
                color: #f59e0b;
                font-weight: 500;
            }

            .math-review-admin-info {
                font-size: 0.75rem;
                color: #6b7280;
                line-height: 1.4;
            }

            .math-review-admin-info p {
                margin: 0;
            }

            /* Manual Override Section - matches English review modal */
            .math-review-manual-override {
                margin-bottom: 1.5rem;
            }

            .math-review-manual-override h3 {
                font-size: 0.9rem;
                font-weight: 600;
                color: #1e3a8a;
                margin-bottom: 0.75rem;
            }

            .math-override-controls {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1.5rem;
            }

            .math-override-score-section {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #ffffff;
            }

            .math-override-score-section h4 {
                margin: 0 0 0.75rem;
                font-size: 0.9rem;
                color: #1e3a8a;
            }

            .math-override-input-group {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
                margin-bottom: 1rem;
            }

            .math-override-input-group label {
                font-size: 0.8rem;
                font-weight: 500;
                color: #374151;
            }

            .math-override-input {
                border: 1px solid #d1d5db;
                border-radius: 4px;
                padding: 0.5rem;
                font-size: 0.8rem;
            }

            .math-override-apply-btn {
                background: #1e3a8a;
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 4px;
                font-size: 0.8rem;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                transition: background-color 0.2s;
            }

            .math-override-apply-btn:hover {
                background: #1e40af;
            }

            .math-override-apply-btn:disabled {
                background: #9ca3af;
                cursor: not-allowed;
            }

            .math-override-explanation {
                font-size: 0.75rem;
                color: #6b7280;
                line-height: 1.4;
            }

            .math-override-current-values {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #f8fafc;
            }

            .math-override-current-values h4 {
                margin: 0 0 0.75rem;
                font-size: 0.9rem;
                color: #1e3a8a;
            }

            .math-current-values-grid {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }

            .math-current-value-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem;
                background: white;
                border-radius: 4px;
                border: 1px solid #e5e7eb;
            }

            .math-current-label {
                font-size: 0.8rem;
                color: #6b7280;
            }

            .math-current-value {
                font-size: 0.8rem;
                font-weight: 500;
                color: #374151;
            }

            /* Error Content - matches English review modal */
            .math-review-error-content {
                text-align: center;
                padding: 2rem 1rem;
                color: #6b7280;
            }

            .math-review-error-icon {
                margin-bottom: 1rem;
                color: #f59e0b;
            }

            .math-review-error-content h3 {
                margin: 0 0 0.5rem;
                color: #374151;
                font-size: 1rem;
            }

            .math-review-error-content p {
                margin: 0;
                line-height: 1.4;
            }

            /* Topic Analysis Section */
            .math-review-topic-analysis {
                margin-bottom: 1.5rem;
            }

            .math-review-topic-analysis h3 {
                font-size: 0.9rem;
                font-weight: 600;
                color: #1e3a8a;
                margin-bottom: 0.75rem;
            }

            .topic-performance-summary {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .performance-stat {
                text-align: center;
                padding: 1rem;
                border-radius: 6px;
                border: 1px solid #e5e7eb;
            }

            .performance-stat.excellent {
                background: #f0fdf4;
                border-color: #059669;
            }

            .performance-stat.good {
                background: #fffbeb;
                border-color: #f59e0b;
            }

            .performance-stat.needs-work {
                background: #fef2f2;
                border-color: #ef4444;
            }

            .stat-number {
                display: block;
                font-size: 1.5rem;
                font-weight: 600;
                color: #1e3a8a;
                margin-bottom: 0.25rem;
            }

            .stat-label {
                font-size: 0.75rem;
                color: #6b7280;
            }

            .detailed-topic-breakdown {
                display: grid;
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .topic-detail-item {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 0.75rem;
                background: #ffffff;
            }

            .topic-detail-item.excellent {
                border-left: 4px solid #059669;
            }

            .topic-detail-item.good {
                border-left: 4px solid #f59e0b;
            }

            .topic-detail-item.needs-work {
                border-left: 4px solid #ef4444;
            }

            .topic-detail-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.5rem;
            }

            .topic-detail-name {
                font-size: 0.8rem;
                font-weight: 500;
                color: #374151;
            }

            .topic-detail-level {
                font-size: 0.7rem;
                color: #6b7280;
                background: #f3f4f6;
                padding: 0.125rem 0.375rem;
                border-radius: 3px;
            }

            .topic-detail-score {
                font-size: 0.75rem;
                font-weight: 600;
                color: #1e3a8a;
            }

            .topic-detail-progress {
                width: 100%;
            }

            .topic-detail-bar {
                width: 100%;
                height: 6px;
                background: #e5e7eb;
                border-radius: 3px;
                overflow: hidden;
            }

            .topic-detail-fill {
                height: 100%;
                transition: width 0.3s ease;
            }

            .topic-detail-fill.excellent {
                background: #059669;
            }

            .topic-detail-fill.good {
                background: #f59e0b;
            }

            .topic-detail-fill.needs-work {
                background: #ef4444;
            }

            /* Question Analysis Section */
            .math-review-question-analysis {
                margin-bottom: 1.5rem;
            }

            .math-review-question-analysis h3 {
                font-size: 0.9rem;
                font-weight: 600;
                color: #1e3a8a;
                margin-bottom: 0.75rem;
            }

            .question-summary-stats {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .question-stat {
                text-align: center;
                padding: 0.75rem;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                background: #f8fafc;
            }

            .question-stat.correct {
                background: #f0fdf4;
                border-color: #059669;
            }

            .question-stat.incorrect {
                background: #fef2f2;
                border-color: #ef4444;
            }

            .level-questions-group {
                margin-bottom: 1.5rem;
            }

            .level-questions-group h4 {
                margin: 0 0 0.75rem;
                font-size: 0.85rem;
                color: #1e3a8a;
                padding-bottom: 0.5rem;
                border-bottom: 1px solid #e5e7eb;
            }

            .questions-grid {
                display: grid;
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .question-item {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #ffffff;
            }

            .question-item.correct {
                border-left: 4px solid #059669;
                background: #f0fdf4;
            }

            .question-item.incorrect {
                border-left: 4px solid #ef4444;
                background: #fef2f2;
            }

            .question-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.75rem;
            }

            .question-number {
                font-size: 0.8rem;
                font-weight: 600;
                color: #1e3a8a;
            }

            .question-topic {
                font-size: 0.75rem;
                color: #6b7280;
                background: #f3f4f6;
                padding: 0.125rem 0.375rem;
                border-radius: 3px;
            }

            .question-result {
                font-size: 1rem;
                font-weight: 600;
            }

            .question-result.correct {
                color: #059669;
            }

            .question-result.incorrect {
                color: #ef4444;
            }

            .question-details {
                font-size: 0.8rem;
            }

            .question-text {
                margin-bottom: 0.75rem;
                padding: 0.5rem;
                background: #f8fafc;
                border-radius: 4px;
                border: 1px solid #e5e7eb;
                color: #374151;
            }

            .question-answers {
                margin-bottom: 0.5rem;
            }

            .student-answer,
            .correct-answer {
                margin-bottom: 0.25rem;
                padding: 0.25rem 0;
            }

            .student-answer {
                color: #4b5563;
            }

            .correct-answer {
                color: #059669;
            }

            .question-meta {
                display: flex;
                gap: 1rem;
                font-size: 0.75rem;
                color: #6b7280;
            }

            .question-time {
                font-weight: 500;
            }

            .no-question-data {
                text-align: center;
                color: #6b7280;
                font-style: italic;
                padding: 1rem;
            }

            /* Interaction Data Section */
            .math-review-interaction-data {
                margin-bottom: 1.5rem;
            }

            .math-review-interaction-data h3,
            .math-review-interaction-data h4 {
                font-size: 0.9rem;
                font-weight: 600;
                color: #1e3a8a;
                margin-bottom: 0.75rem;
            }

            .interaction-metadata {
                margin-bottom: 1.5rem;
            }

            .metadata-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 0.75rem;
            }

            .metadata-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem;
                background: #f8fafc;
                border: 1px solid #e5e7eb;
                border-radius: 4px;
                font-size: 0.8rem;
            }

            .metadata-label {
                color: #6b7280;
                font-weight: 500;
            }

            .metadata-value {
                color: #374151;
                font-weight: 600;
            }

            .browser-information {
                margin-bottom: 1.5rem;
            }

            .browser-details {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 0.5rem;
            }

            .browser-item {
                display: flex;
                justify-content: space-between;
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 4px;
            }

            .browser-label {
                color: #6b7280;
            }

            .browser-value {
                color: #374151;
                font-weight: 500;
            }

            .interaction-log {
                margin-bottom: 1rem;
            }

            .interaction-events {
                max-height: 200px;
                overflow-y: auto;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                background: #ffffff;
            }

            .interaction-event {
                display: flex;
                gap: 1rem;
                padding: 0.5rem;
                border-bottom: 1px solid #f3f4f6;
                font-size: 0.75rem;
            }

            .interaction-event:last-child {
                border-bottom: none;
            }

            .event-time {
                color: #6b7280;
                font-weight: 500;
                min-width: 80px;
            }

            .event-type {
                color: #1e3a8a;
                font-weight: 600;
                min-width: 100px;
            }

            .event-details {
                color: #374151;
                flex: 1;
            }

            .no-interaction-data {
                text-align: center;
                color: #6b7280;
                font-style: italic;
                padding: 1rem;
            }

            /* Enhanced no-data styling */
            .no-question-data {
                text-align: center;
                padding: 2rem 1rem;
                background: #f8fafc;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                margin: 1rem 0;
            }

            .no-question-data p {
                margin: 0 0 1rem;
                color: #6b7280;
            }

            .data-note {
                font-weight: 500;
                color: #4b5563;
                margin-bottom: 0.5rem !important;
            }

            .data-reasons {
                text-align: left;
                max-width: 400px;
                margin: 0 auto;
                padding-left: 1rem;
            }

            .data-reasons li {
                margin-bottom: 0.25rem;
                color: #6b7280;
                font-size: 0.8rem;
            }

            /* Responsive Design - matches English review modal */
            @media (max-width: 768px) {
                .math-review-modal-content {
                    margin: 0.5rem;
                    max-height: calc(100vh - 1rem);
                    font-size: 0.8rem;
                }

                .math-review-modal-header {
                    padding: 0.5rem;
                }

                .math-review-modal-body {
                    padding: 0.75rem;
                }

                .math-review-feedback-grid {
                    grid-template-columns: 1fr;
                    gap: 0.5rem;
                }

                .math-review-levels-grid {
                    grid-template-columns: 1fr;
                }

                .math-review-strengths-improvements {
                    grid-template-columns: 1fr;
                    gap: 0.75rem;
                }

                .math-override-controls {
                    grid-template-columns: 1fr;
                    gap: 1rem;
                }

                .topic-performance-summary {
                    grid-template-columns: 1fr;
                    gap: 0.75rem;
                }

                .question-summary-stats {
                    grid-template-columns: repeat(2, 1fr);
                    gap: 0.75rem;
                }

                .metadata-grid {
                    grid-template-columns: 1fr;
                    gap: 0.5rem;
                }

                .browser-details {
                    grid-template-columns: 1fr;
                    gap: 0.25rem;
                }

                .math-review-assessment-meta {
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .math-review-admin-buttons {
                    flex-direction: column;
                    align-items: stretch;
                }

                .math-admin-review-btn {
                    justify-content: center;
                }
            }

            @media (max-width: 480px) {
                .math-review-modal-content {
                    margin: 0.25rem;
                    max-height: calc(100vh - 0.5rem);
                }

                .math-review-score-value {
                    font-size: 1.5rem;
                }

                .math-review-modal-employee-title {
                    font-size: 0.9rem;
                }

                .math-review-modal-subtitle {
                    font-size: 0.75rem;
                }
            }
        `;
        document.head.appendChild(styles);
    }

    /**
     * Initialize review event listeners
     */
    function initializeReviewEventListeners(overlay) {
        // Close modal on overlay click
        overlay.addEventListener('click', reviewOverlayClickHandler);

        // Close button
        const closeBtn = overlay.querySelector('#close-math-review-modal');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                console.log('Close button clicked');
                hideReviewModal();
            });
        }

        // Admin review buttons
        const viewResponsesBtn = overlay.querySelector('#view-math-detailed-responses');
        if (viewResponsesBtn) {
            viewResponsesBtn.addEventListener('click', handleViewMathDetailedResponses);
        }

        const exportBtn = overlay.querySelector('#export-math-assessment-data');
        if (exportBtn) {
            exportBtn.addEventListener('click', handleExportMathAssessmentData);
        }

        // Score override functionality
        const overrideBtn = overlay.querySelector('#math-apply-score-override');
        if (overrideBtn) {
            overrideBtn.addEventListener('click', handleMathScoreOverride);
        }

        // Question analysis expand/collapse
        const questionItems = overlay.querySelectorAll('.question-item');
        questionItems.forEach(item => {
            const header = item.querySelector('.question-header');
            if (header) {
                header.addEventListener('click', () => {
                    const details = item.querySelector('.question-details');
                    if (details) {
                        if (details.style.display === 'none') {
                            details.style.display = 'block';
                        } else {
                            details.style.display = 'none';
                        }
                    }
                });
            }
        });

        // Interaction log expand/collapse
        const logHeader = overlay.querySelector('.interaction-log h4');
        if (logHeader) {
            logHeader.addEventListener('click', () => {
                const events = overlay.querySelector('.interaction-events');
                if (events) {
                    if (events.style.display === 'none') {
                        events.style.display = 'block';
                        logHeader.textContent = 'Interaction Timeline (Last 10 events) ▼';
                    } else {
                        events.style.display = 'none';
                        logHeader.textContent = 'Interaction Timeline (Last 10 events) ►';
                    }
                }
            });
        }
    }

    /**
     * Handle overlay click to close modal
     */
    function reviewOverlayClickHandler(event) {
        if (event.target.id === 'math-review-overlay') {
            hideReviewModal();
        }
    }

    /**
     * Handle view detailed mathematics responses
     */
    function handleViewMathDetailedResponses() {
        if (!currentReviewData || !currentReviewData.mathAssessmentResponses) {
            if (typeof showNotification === 'function') {
                showNotification('No detailed response data available', 'error');
            }
            return;
        }

        // Show detailed responses in a new window (similar to English modal)
        const responses = currentReviewData.mathAssessmentResponses.questionResponses || [];
        let reviewText = `Mathematics Assessment Review - ${currentReviewData.firstName} ${currentReviewData.lastName}\n`;
        reviewText += `Email: ${currentUserEmail}\n`;
        reviewText += `Assessment Date: ${new Date().toLocaleDateString()}\n\n`;

        responses.forEach((response, index) => {
            reviewText += `Question ${index + 1}:\n`;
            reviewText += `Level: ${response.level || 'Unknown'}\n`;
            reviewText += `Topic: ${response.topic || 'Unknown'}\n`;
            reviewText += `Question: ${response.questionText || 'Not available'}\n`;
            reviewText += `Correct Answer: ${response.correctAnswer || 'Not available'}\n`;
            reviewText += `Student Answer: ${response.studentAnswer || 'No response'}\n`;
            reviewText += `Result: ${response.isCorrect ? 'CORRECT' : 'INCORRECT'}\n`;
            reviewText += `Time Spent: ${response.timeSpent || 0}ms\n\n`;
        });

        // Show in a new window
        const reviewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
        reviewWindow.document.write(`
            <html>
                <head>
                    <title>Mathematics Assessment Review</title>
                    <style>
                        body { font-family: monospace; padding: 20px; line-height: 1.6; }
                        h1 { color: #333; }
                        .correct { color: green; }
                        .incorrect { color: red; }
                    </style>
                </head>
                <body>
                    <h1>Mathematics Assessment Review</h1>
                    <pre>${reviewText}</pre>
                    <button onclick="window.close()">Close</button>
                </body>
            </html>
        `);
    }

    /**
     * Handle export mathematics assessment data
     */
    function handleExportMathAssessmentData() {
        if (!currentReviewData) {
            if (typeof showNotification === 'function') {
                showNotification('No assessment data available for export', 'error');
            }
            return;
        }

        // Create CSV data
        const csvData = [
            ['Field', 'Value'],
            ['Student Name', `${currentReviewData.firstName || ''} ${currentReviewData.lastName || ''}`],
            ['Email', currentUserEmail || ''],
            ['Overall Score', currentReviewData.mathOverallScore || 0],
            ['Current Level', currentReviewData.mathCurrentLevel || 'Entry'],
            ['Highest Level Completed', currentReviewData.mathHighestLevelCompleted || 'None'],
            ['Assessment Completed', currentReviewData.mathAssessmentCompleted ? 'Yes' : 'No'],
            ['Time Spent (minutes)', Math.round((currentReviewData.totalTimeSpentOnMath || 0) / 60)],
            ['Assessment Date', currentReviewData.mathAssessmentTimestamp ?
                new Date(currentReviewData.mathAssessmentTimestamp.toDate ?
                    currentReviewData.mathAssessmentTimestamp.toDate() :
                    currentReviewData.mathAssessmentTimestamp).toLocaleDateString() : 'Not available']
        ];

        // Convert to CSV string
        const csvString = csvData.map(row => row.map(field => `"${field}"`).join(',')).join('\n');

        // Download CSV
        const blob = new Blob([csvString], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `math-assessment-${currentUserEmail}-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        if (typeof showNotification === 'function') {
            showNotification('Assessment data exported successfully', 'success');
        }
    }

    /**
     * Handle mathematics score override
     */
    async function handleMathScoreOverride() {
        const scoreInput = document.getElementById('math-override-score-input');
        const overrideBtn = document.getElementById('math-apply-score-override');

        if (!scoreInput || !overrideBtn) return;

        const newScore = parseInt(scoreInput.value);

        if (isNaN(newScore) || newScore < 0 || newScore > 100) {
            if (typeof showNotification === 'function') {
                showNotification('Please enter a valid score between 0 and 100', 'error');
            }
            return;
        }

        // Show loading state
        const btnText = overrideBtn.querySelector('.btn-text');
        const btnLoading = overrideBtn.querySelector('.btn-loading');

        if (btnText) btnText.style.display = 'none';
        if (btnLoading) btnLoading.style.display = 'flex';
        overrideBtn.disabled = true;

        try {
            // Calculate new level based on score
            const newLevel = calculateMathLevelFromScore(newScore);

            // Apply override to Firebase
            const success = await applyMathScoreOverride(currentUserEmail, currentUserCompany, newScore, newLevel);

            if (success) {
                // Update current data
                currentReviewData.manualScoreOverride = {
                    originalScore: currentReviewData.mathOverallScore,
                    newScore: newScore,
                    calculatedLevel: newLevel,
                    overrideDate: new Date(),
                    overrideBy: 'admin' // Could be enhanced to track specific admin
                };

                // Update UI
                const statusElement = document.getElementById('math-override-status');
                if (statusElement) {
                    statusElement.textContent = 'Score Override Active';
                }

                // Refresh modal content
                await resetAndShowReviewModal(currentReviewData.firstName && currentReviewData.lastName ?
                    `${currentReviewData.firstName} ${currentReviewData.lastName}` : 'Student');

                if (typeof showNotification === 'function') {
                    showNotification('Score override applied successfully', 'success');
                }
            } else {
                throw new Error('Failed to apply override');
            }
        } catch (error) {
            console.error('Error applying mathematics score override:', error);
            if (typeof showNotification === 'function') {
                showNotification('Failed to apply score override', 'error');
            }
        } finally {
            // Reset button state
            if (btnText) btnText.style.display = 'inline';
            if (btnLoading) btnLoading.style.display = 'none';
            overrideBtn.disabled = false;
        }
    }

    /**
     * Calculate mathematics level from score
     */
    function calculateMathLevelFromScore(score) {
        if (score >= 80) return 'GCSE Higher';
        if (score >= 60) return 'GCSE Foundation';
        if (score >= 40) return 'Level 1';
        return 'Entry';
    }

    /**
     * Apply mathematics score override to Firebase
     */
    async function applyMathScoreOverride(userEmail, userCompany, newScore, newLevel) {
        try {
            if (typeof db === 'undefined') {
                console.error('Database not initialized');
                return false;
            }

            const userRef = db.collection('companies').doc(userCompany).collection('users').doc(userEmail);

            await userRef.update({
                manualScoreOverride: {
                    originalScore: currentReviewData.mathOverallScore,
                    newScore: newScore,
                    calculatedLevel: newLevel,
                    overrideDate: firebase.firestore.FieldValue.serverTimestamp(),
                    overrideBy: 'admin'
                },
                mathCurrentLevel: newLevel,
                mathOverallScore: newScore
            });

            return true;
        } catch (error) {
            console.error('Error applying mathematics score override:', error);
            return false;
        }
    }

    // Utility functions for level breakdown
    function getMaxScore(level) {
        const maxScores = {
            'entrylevel': 32,
            'level1': 25,
            'gcsepart1': 30,
            'gcsepart2': 20
        };
        return maxScores[level] || 100;
    }

    function formatDuration(milliseconds) {
        if (!milliseconds) return 'Not recorded';
        const minutes = Math.round(milliseconds / 60000);
        return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
    }

    function formatDate(timestamp) {
        if (!timestamp) return 'Not available';
        const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
        return date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    }



    // Review validation helper functions
    function validateReviewLevel(level) {
        const validLevels = ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2', 'GCSE Foundation', 'GCSE Higher'];
        return validLevels.includes(level) ? level : null;
    }

    function validateReviewScore(score) {
        const numScore = Number(score);
        return !isNaN(numScore) && numScore >= 0 && numScore <= 100 ? numScore : null;
    }

    function validateReviewTimestamp(timestamp) {
        if (!timestamp) return null;
        if (timestamp.toDate && typeof timestamp.toDate === 'function') return timestamp;
        if (timestamp instanceof Date) return timestamp;
        const date = new Date(timestamp);
        return !isNaN(date.getTime()) ? date : null;
    }

    function validateReviewTimeSpent(timeSpent) {
        const numTime = Number(timeSpent);
        return !isNaN(numTime) && numTime >= 0 ? numTime : null;
    }

    function validateReviewString(str) {
        return typeof str === 'string' && str.trim().length > 0 ? str.trim() : null;
    }

    function validateReviewArray(arr) {
        return Array.isArray(arr) ? arr : null;
    }

    function validateReviewFeedback(feedback) {
        if (!feedback || typeof feedback !== 'object') return null;
        return feedback;
    }

    function validateReviewPlacementRecommendation(recommendation) {
        if (!recommendation || typeof recommendation !== 'object') return null;
        return recommendation;
    }

    function validateReviewOverride(override) {
        if (!override || typeof override !== 'object') return null;
        return override;
    }

    function validateReviewLevelData(levelData, levelType) {
        if (!levelData || typeof levelData !== 'object') {
            return createDefaultReviewLevelData(levelType);
        }

        console.log(`Validating ${levelType} data:`, levelData);

        return {
            completed: Boolean(levelData.completed),
            score: validateReviewScore(levelData.score) || 0,
            passed: Boolean(levelData.passed),
            timeSpent: validateReviewTimeSpent(levelData.timeSpent) || 0,
            completedAt: validateReviewTimestamp(levelData.completedAt) || null,
            responses: validateReviewArray(levelData.responses) || [],
            topicBreakdown: validateReviewTopicBreakdown(levelData.topicBreakdown, levelType)
        };
    }

    function validateReviewTopicBreakdown(breakdown, levelType) {
        const defaultBreakdowns = {
            'Entry': {
                arithmetic: { score: 0, maxScore: 8 },
                fractions: { score: 0, maxScore: 6 },
                percentages: { score: 0, maxScore: 4 },
                basicAlgebra: { score: 0, maxScore: 6 },
                measurement: { score: 0, maxScore: 4 },
                dataHandling: { score: 0, maxScore: 4 }
            },
            'Level1': {
                advancedArithmetic: { score: 0, maxScore: 4 },
                fractionsDecimals: { score: 0, maxScore: 4 },
                percentagesRatio: { score: 0, maxScore: 4 },
                algebraicExpressions: { score: 0, maxScore: 6 },
                geometry: { score: 0, maxScore: 4 },
                statistics: { score: 0, maxScore: 4 }
            },
            'GCSEPart1': {
                numberOperations: { score: 0, maxScore: 3 },
                algebraicManipulation: { score: 0, maxScore: 3 },
                geometricReasoning: { score: 0, maxScore: 2 },
                fractionalCalculations: { score: 0, maxScore: 2 }
            },
            'GCSEPart2': {
                complexCalculations: { score: 0, maxScore: 4 },
                statisticalAnalysis: { score: 0, maxScore: 4 },
                trigonometry: { score: 0, maxScore: 4 },
                advancedAlgebra: { score: 0, maxScore: 4 },
                problemSolving: { score: 0, maxScore: 4 }
            }
        };

        const defaultBreakdown = defaultBreakdowns[levelType] || {};

        console.log(`Validating topic breakdown for ${levelType}:`, breakdown);

        if (!breakdown || typeof breakdown !== 'object') {
            console.log(`No breakdown data for ${levelType}, using defaults`);
            return defaultBreakdown;
        }

        // If the breakdown exists, preserve the actual data
        const validatedBreakdown = {};

        // First, copy any existing data from the breakdown
        Object.keys(breakdown).forEach(topic => {
            if (breakdown[topic] && typeof breakdown[topic] === 'object') {
                validatedBreakdown[topic] = {
                    score: validateReviewScore(breakdown[topic].score) || 0,
                    maxScore: validateReviewScore(breakdown[topic].maxScore) || breakdown[topic].maxScore || 0
                };
            }
        });

        // Then, fill in any missing topics with defaults
        Object.keys(defaultBreakdown).forEach(topic => {
            if (!validatedBreakdown[topic]) {
                validatedBreakdown[topic] = defaultBreakdown[topic];
            }
        });

        console.log(`Validated breakdown for ${levelType}:`, validatedBreakdown);
        return validatedBreakdown;
    }

    function validateReviewAssessmentResponses(responses) {
        if (!responses || typeof responses !== 'object') {
            return {
                questionResponses: [],
                assessmentMetadata: {
                    sessionId: null,
                    userAgent: null,
                    startTime: null,
                    endTime: null,
                    totalInteractions: 0,
                    calculatorUsed: false,
                    assistiveTechnologyUsed: false,
                    browserInfo: {}
                },
                interactionLog: []
            };
        }

        return {
            questionResponses: validateReviewArray(responses.questionResponses) || [],
            assessmentMetadata: validateReviewAssessmentMetadata(responses.assessmentMetadata),
            interactionLog: validateReviewArray(responses.interactionLog) || []
        };
    }

    function validateReviewAssessmentMetadata(metadata) {
        if (!metadata || typeof metadata !== 'object') {
            return {
                sessionId: null,
                userAgent: null,
                startTime: null,
                endTime: null,
                totalInteractions: 0,
                calculatorUsed: false,
                assistiveTechnologyUsed: false,
                browserInfo: {}
            };
        }

        return {
            sessionId: validateReviewString(metadata.sessionId) || null,
            userAgent: validateReviewString(metadata.userAgent) || null,
            startTime: validateReviewTimestamp(metadata.startTime) || null,
            endTime: validateReviewTimestamp(metadata.endTime) || null,
            totalInteractions: Number(metadata.totalInteractions) || 0,
            calculatorUsed: Boolean(metadata.calculatorUsed),
            assistiveTechnologyUsed: Boolean(metadata.assistiveTechnologyUsed),
            browserInfo: (metadata.browserInfo && typeof metadata.browserInfo === 'object') ? metadata.browserInfo : {}
        };
    }

    function createDefaultReviewLevelData(levelType) {
        return {
            completed: false,
            score: 0,
            passed: false,
            timeSpent: 0,
            completedAt: null,
            responses: [],
            topicBreakdown: validateReviewTopicBreakdown(null, levelType)
        };
    }

})();
