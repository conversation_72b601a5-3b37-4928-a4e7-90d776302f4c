/**
 * Unified Assessment Results Modal Styles
 * Consistent design system for English, Math, and Digital Skills assessment modals
 * Uses 8px spacing grid and Skills Gap Analysis modal dimensions
 */

/* Modal Overlay - Consistent across all assessment types */
.assessment-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
    padding: 16px;
}

/* Modal Content Container - Unified design */
.assessment-modal-content {
    background: #ffffff;
    border-radius: 16px;
    width: 100%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.95);
    opacity: 0;
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;
    font-family: 'Montserrat', sans-serif;
}

/* Modal Header - Consistent styling */
.assessment-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px 32px 16px;
    border-bottom: 1px solid #e0e0e0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 16px 16px 0 0;
}

.assessment-modal-title-container {
    flex: 1;
}

.assessment-modal-student-title {
    color: #333333;
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 4px 0;
    line-height: 1.2;
}

.assessment-modal-subtitle {
    color: #666666;
    font-size: 16px;
    font-weight: 400;
    margin: 0;
    line-height: 1.3;
}

.assessment-modal-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.assessment-export-button {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.assessment-export-button:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.assessment-close-button {
    background: transparent;
    border: none;
    color: #666666;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.assessment-close-button:hover {
    background: #f0f0f0;
    color: #333333;
}

/* Modal Body - Consistent padding and typography */
.assessment-modal-body {
    padding: 24px 32px 32px;
    color: #333333;
    line-height: 1.5;
}

/* Section Styling - Unified across all assessments */
.assessment-section {
    margin-bottom: 32px;
}

.assessment-section:last-child {
    margin-bottom: 0;
}

.assessment-section h3 {
    color: #333333;
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #e0e0e0;
}

.assessment-section h4 {
    color: #333333;
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 12px 0;
}

/* Assessment Overview Grid - Consistent card layout */
.assessment-overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.assessment-overview-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: transform 0.2s ease;
}

.assessment-overview-card:hover {
    transform: translateY(-2px);
}

.assessment-card-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 12px;
}

.assessment-card-icon {
    font-size: 20px;
}

.assessment-card-title {
    color: #666666;
    font-size: 14px;
    font-weight: 500;
}

.assessment-card-value {
    color: #333333;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
    line-height: 1;
}

.assessment-card-status {
    font-size: 14px;
    font-weight: 500;
    padding: 4px 12px;
    border-radius: 20px;
    display: inline-block;
}

.assessment-card-status.passed {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid #4CAF50;
}

.assessment-card-status.failed {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
    border: 1px solid #f44336;
}

.assessment-card-status.qualified {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid #4CAF50;
}

.assessment-card-status.needs-improvement {
    background: rgba(255, 152, 0, 0.2);
    color: #FF9800;
    border: 1px solid #FF9800;
}

.assessment-card-subtitle {
    color: #888888;
    font-size: 12px;
    margin-top: 4px;
}

/* AI Analysis & Feedback - Consistent styling */
.assessment-feedback-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.assessment-feedback-item {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
}

.assessment-feedback-category {
    color: #4CAF50;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.assessment-feedback-category::before {
    content: "🤖";
    font-size: 18px;
}

.assessment-feedback-text {
    color: #555555;
    font-size: 14px;
    line-height: 1.6;
}

/* Strengths and Improvements - Unified grid layout */
.assessment-strengths-improvements-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

.assessment-strengths-container,
.assessment-improvements-container {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
}

.assessment-strengths-title {
    color: #4CAF50;
    margin-bottom: 16px;
}

.assessment-improvements-title {
    color: #FF9800;
    margin-bottom: 16px;
}

.assessment-strengths-list,
.assessment-improvements-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.assessment-strength-item,
.assessment-improvement-item,
.assessment-no-data-item {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
}

.assessment-strength-item:last-child,
.assessment-improvement-item:last-child,
.assessment-no-data-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.assessment-strength-item {
    color: #555555;
}

.assessment-improvement-item {
    color: #555555;
}

.assessment-no-data-item {
    color: #888888;
    font-style: italic;
}

/* Time Analytics - Consistent card layout */
.assessment-time-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
}

.assessment-time-card {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
}

.assessment-time-icon {
    font-size: 24px;
    margin-bottom: 12px;
}

.assessment-time-value {
    color: #333333;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
}

.assessment-time-label {
    color: #666666;
    font-size: 12px;
}

/* No Data States - Consistent messaging */
.assessment-no-data {
    text-align: center;
    padding: 40px 20px;
    color: #888888;
}

.assessment-no-data p {
    font-size: 14px;
    margin: 0;
}

/* Responsive Design - Consistent breakpoints */
@media (max-width: 768px) {
    .assessment-modal-content {
        margin: 8px;
        max-height: calc(100vh - 16px);
    }
    
    .assessment-modal-header {
        padding: 16px 20px 12px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .assessment-modal-actions {
        align-self: flex-end;
    }
    
    .assessment-modal-body {
        padding: 16px 20px 24px;
    }
    
    .assessment-overview-grid {
        grid-template-columns: 1fr;
    }
    
    .assessment-strengths-improvements-grid {
        grid-template-columns: 1fr;
    }
    
    .assessment-time-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .assessment-modal-student-title {
        font-size: 20px;
    }
    
    .assessment-modal-subtitle {
        font-size: 14px;
    }
    
    .assessment-section h3 {
        font-size: 18px;
    }
    
    .assessment-card-value {
        font-size: 24px;
    }
}
