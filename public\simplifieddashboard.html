<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simplified Admin Dashboard - Skills Assess</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Firebase -->
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>
    
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">

    <!-- Heroicons (Tailwind Icons) -->
    <script src="https://unpkg.com/heroicons@2.0.18/24/outline/index.js"></script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="user-menu.css">

    <!-- Unified Assessment Modal Styles -->
    <link rel="stylesheet" href="unified-assessment-modal.css">
    <link rel="stylesheet" href="digital-skills-results-modal.css">
    
    <style>
        /* Custom styles for simplified dashboard */
        .simplified-dashboard {
            font-family: 'Montserrat', sans-serif;
            background: url('BG.png') center center/cover no-repeat fixed;
            min-height: 100vh;
            position: relative;
        }

        .simplified-dashboard::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.1);
            z-index: -1;
        }

        .dashboard-container {
            width: 100%;
            margin: 0;
            padding: 2rem;
            overflow: hidden; /* Prevent horizontal scroll on main container */
        }

        .dashboard-header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .dashboard-header:hover {
            transform: translateY(-2px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 10px 20px rgba(0, 0, 0, 0.08);
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1e3a8a;
            margin-bottom: 0.5rem;
            transition: color 0.3s ease;
        }

        .dashboard-subtitle {
            color: #64748b;
            font-size: 1rem;
            line-height: 1.5;
        }

        /* Summary Cards Styles */
        .summary-cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
            background: rgba(0, 0, 0, 0.02);
            backdrop-filter: blur(10px);
            padding: 1rem;
            border-radius: 16px;
            margin-top: 1rem;
        }

        .summary-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .summary-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 10px 20px rgba(0, 0, 0, 0.08);
        }

        .summary-card-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .summary-card-icon svg {
            width: 1.5rem;
            height: 1.5rem;
        }

        #total-students-card .summary-card-icon {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
        }

        #english-completed-card .summary-card-icon {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        #math-completed-card .summary-card-icon {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        #overall-completion-card .summary-card-icon {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
        }

        .summary-card-content {
            flex: 1;
            min-width: 0;
        }

        .summary-card-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1e3a8a;
            line-height: 1;
            margin-bottom: 0.25rem;
        }

        .summary-card-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #64748b;
            margin-bottom: 0.25rem;
        }

        .summary-card-percentage {
            font-size: 0.75rem;
            font-weight: 600;
            color: #10b981;
        }

        .summary-card-sublabel {
            font-size: 0.75rem;
            color: #94a3b8;
            font-style: italic;
        }

        /* Summary Card Skeleton Loaders */
        .summary-card-skeleton {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .skeleton-card-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 12px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            flex-shrink: 0;
        }

        .skeleton-card-content {
            flex: 1;
            min-width: 0;
        }

        .skeleton-card-value {
            height: 2rem;
            width: 4rem;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 6px;
            margin-bottom: 0.5rem;
        }

        .skeleton-card-label {
            height: 0.875rem;
            width: 6rem;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
            margin-bottom: 0.25rem;
        }

        .skeleton-card-percentage {
            height: 0.75rem;
            width: 3rem;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
        }

        /* Hide real cards during loading */
        .summary-cards-container.loading .summary-card {
            display: none;
        }

        .summary-cards-container.loading .summary-card-skeleton {
            display: flex;
        }

        .summary-cards-container:not(.loading) .summary-card-skeleton {
            display: none;
        }

        /* Summary Card Skeleton Loaders */
        .summary-card-skeleton {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .skeleton-card-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 12px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            flex-shrink: 0;
        }

        .skeleton-card-content {
            flex: 1;
            min-width: 0;
        }

        .skeleton-card-value {
            height: 2rem;
            width: 4rem;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 6px;
            margin-bottom: 0.5rem;
        }

        .skeleton-card-label {
            height: 0.875rem;
            width: 6rem;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
            margin-bottom: 0.25rem;
        }

        .skeleton-card-percentage {
            height: 0.75rem;
            width: 3rem;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
        }

        /* Hide real cards during loading */
        .summary-cards-container.loading .summary-card {
            display: none;
        }

        .summary-cards-container.loading .summary-card-skeleton {
            display: flex;
        }

        .summary-cards-container:not(.loading) .summary-card-skeleton {
            display: none;
        }
        
        .students-table-container {
            background: #ffffff;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease;
            display: flex;
            flex-direction: column;
            height: calc(100vh - 400px);
            min-height: 600px;
            max-height: 800px;
            position: relative;
            z-index: 1;
        }

        .students-table-container:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
        }

        /* Ensure user menu interactions are not blocked */
        .students-table-container,
        .table-scroll-container {
            pointer-events: auto; /* Ensure pointer events work normally */
        }

        /* Prevent any backdrop filters from interfering with user menu */
        .students-table-container {
            isolation: isolate; /* Create new stacking context without affecting z-index */
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-shrink: 0; /* Prevent header from shrinking */
        }

        .table-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e3a8a;
            transition: color 0.3s ease;
        }

        /* Search and Filter Section */
        .search-filter-section {
            flex-shrink: 0; /* Prevent search section from shrinking */
            margin-bottom: 1rem;
        }

        /* Scrollable Table Area */
        .table-scroll-container {
            flex: 1;
            overflow-y: auto;
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            background: #ffffff;
            scroll-behavior: smooth;
            position: relative;
            z-index: 2; /* Low z-index, but higher than parent container */
        }

        .table-scroll-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .table-scroll-container::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .table-scroll-container::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        .table-scroll-container::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Scroll shadow indicators */
        .table-scroll-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(to bottom, rgba(0,0,0,0.1), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 5;
        }

        .table-scroll-container.scrolled::before {
            opacity: 1;
        }

        /* Sticky Table Header */
        .students-table thead {
            position: sticky;
            top: 0;
            z-index: 10;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .students-table thead th {
            background: #f8fafc;
            box-shadow: none;
            transition: box-shadow 0.2s ease;
        }

        /* Enhanced header shadow when scrolling */
        .table-scroll-container.scrolled .students-table thead th {
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .students-count {
            background: #1e40af;
            color: white;
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.75rem;
            box-shadow: 0 1px 3px rgba(30, 64, 175, 0.3);
            transition: all 0.2s ease;
        }

        .students-count:hover {
            background: #1d4ed8;
            box-shadow: 0 2px 4px rgba(30, 64, 175, 0.4);
        }

        .students-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0; /* Remove margin since it's inside scroll container */
        }

        .students-table th {
            background: #f8fafc;
            padding: 0.75rem 1rem;
            text-align: left;
            font-weight: 600;
            color: #1e40af;
            border-bottom: 1px solid #e2e8f0;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            transition: all 0.2s ease;
        }

        .students-table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
            font-size: 0.8125rem;
            transition: all 0.2s ease;
        }

        .students-table tr:hover {
            background: #f8fafc;
            transition: all 0.2s ease;
        }

        .students-table tr:hover td {
            border-color: #e2e8f0;
        }
        
        .assessment-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .assessment-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .assessment-badge:active {
            transform: translateY(0);
            transition: transform 0.1s ease;
        }

        .badge-completed {
            background: #f1f5f9;
            color: #475569;
            border-color: #cbd5e1;
        }

        .badge-not-completed {
            background: #f8fafc;
            color: #64748b;
            border-color: #e2e8f0;
        }

        /* Professional blue theme badge styles */
        .badge-excellent {
            background: #dbeafe;
            color: #1e40af;
            border-color: #93c5fd;
        }

        .badge-good {
            background: #e0f2fe;
            color: #0369a1;
            border-color: #7dd3fc;
        }

        .badge-fair {
            background: #fef3c7;
            color: #d97706;
            border-color: #fde68a;
        }

        .badge-needs-work {
            background: #fee2e2;
            color: #dc2626;
            border-color: #fca5a5;
        }

        .badge-pass {
            background: #dcfce7;
            color: #166534;
            border-color: #86efac;
        }

        .badge-fail {
            background: #fee2e2;
            color: #dc2626;
            border-color: #fca5a5;
        }

        /* Badge icons */
        .badge-icon {
            width: 0.875rem;
            height: 0.875rem;
            flex-shrink: 0;
        }
        
        .skeleton-loader {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 6px;
            height: 0.875rem;
        }

        .skeleton-loader.wide {
            width: 100%;
        }

        .skeleton-loader.medium {
            width: 60%;
        }

        .skeleton-loader.narrow {
            width: 40%;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(4px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.3s ease;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #1e3a8a;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Toast notifications */
        .toast-notification {
            position: fixed;
            top: 1rem;
            right: 1rem;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            font-weight: 500;
            font-size: 0.875rem;
            z-index: 10000;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .toast-notification.show {
            transform: translateX(0);
        }

        .toast-error {
            background: rgba(239, 68, 68, 0.95);
            color: white;
        }

        .toast-success {
            background: rgba(34, 197, 94, 0.95);
            color: white;
        }
        
        /* Header improvements */
        header {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border-bottom: 1px solid rgba(229, 231, 235, 0.5);
            transition: all 0.3s ease;
            position: relative;
            z-index: 50; /* Above table content, below user menu */
        }

        header:hover {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        /* Responsive design */
        @media (max-width: 1024px) {
            .dashboard-container {
                padding: 1rem;
            }

            .summary-cards-container {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .summary-card {
                padding: 1.25rem;
            }

            .summary-card-value {
                font-size: 1.75rem;
            }

            .table-title {
                font-size: 1.125rem;
            }

            .students-table-container {
                padding: 1.5rem;
                height: calc(100vh - 350px); /* Adjust for smaller screens */
                min-height: 500px;
                max-height: 700px;
            }

            .table-scroll-container {
                border-radius: 6px;
            }

            .students-table {
                min-width: 1000px;
            }

            .students-table th,
            .students-table td {
                padding: 0.625rem 0.75rem;
                font-size: 0.75rem;
            }
        }

        @media (max-width: 640px) {
            .dashboard-container {
                padding: 0.75rem;
            }

            .summary-cards-container {
                grid-template-columns: 1fr;
                gap: 0.75rem;
                margin-bottom: 1.25rem;
            }

            .summary-card {
                padding: 1rem;
                flex-direction: column;
                text-align: center;
                gap: 0.75rem;
            }

            .summary-card-icon {
                width: 2.5rem;
                height: 2.5rem;
            }

            .summary-card-icon svg {
                width: 1.25rem;
                height: 1.25rem;
            }

            .summary-card-value {
                font-size: 1.5rem;
            }

            .summary-card-skeleton {
                padding: 1rem;
                flex-direction: column;
                text-align: center;
                gap: 0.75rem;
            }

            .skeleton-card-icon {
                width: 2.5rem;
                height: 2.5rem;
            }

            .table-header {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
                margin-bottom: 1.5rem;
            }

            .students-table-container {
                padding: 1rem;
                height: calc(100vh - 300px); /* Further adjust for mobile */
                min-height: 400px;
                max-height: 600px;
            }

            .search-filter-section {
                padding: 1rem;
                margin-bottom: 1.5rem;
            }

            .search-filter-row {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .search-bar-container {
                min-width: auto;
                width: 100%;
            }

            .filter-controls {
                flex-direction: column;
                gap: 0.75rem;
                width: 100%;
            }

            .filter-select {
                width: 100%;
                min-width: auto;
            }

            .clear-filters-btn {
                width: 100%;
                justify-content: center;
            }

            .students-table th,
            .students-table td {
                padding: 0.5rem 0.375rem;
                font-size: 0.6875rem;
            }

            .assessment-badge {
                padding: 0.1875rem 0.375rem;
                font-size: 0.6875rem;
            }

            .badge-icon {
                width: 0.75rem;
                height: 0.75rem;
            }

            .toast-notification {
                top: 0.5rem;
                right: 0.5rem;
                left: 0.5rem;
                font-size: 0.8125rem;
            }
        }

        /* Smooth transitions for all interactive elements */
        * {
            transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
        }

        /* Search and Filter Styles */
        .search-filter-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: rgba(248, 250, 252, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            border: 1px solid rgba(226, 232, 240, 0.5);
        }

        .search-filter-row {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-bar-container {
            flex: 1;
            min-width: 300px;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            width: 1.25rem;
            height: 1.25rem;
            color: #64748b;
            z-index: 1;
        }

        .search-input {
            width: 100%;
            padding: 0.625rem 0.875rem 0.625rem 2.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #ffffff;
            font-size: 0.8125rem;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .search-input:focus {
            outline: none;
            border-color: #1e40af;
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
        }

        .clear-search-btn {
            position: absolute;
            right: 0.625rem;
            width: 1.25rem;
            height: 1.25rem;
            color: #64748b;
            background: none;
            border: none;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .clear-search-btn:hover {
            color: #dc2626;
            background: rgba(220, 38, 38, 0.1);
        }

        .filter-controls {
            display: flex;
            gap: 0.75rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-select {
            padding: 0.625rem 0.875rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #ffffff;
            font-size: 0.8125rem;
            color: #374151;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            min-width: 140px;
        }

        .filter-select:focus {
            outline: none;
            border-color: #1e40af;
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
        }

        .clear-filters-btn {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.625rem 0.875rem;
            background: #dc2626;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.8125rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(220, 38, 38, 0.3);
        }

        .clear-filters-btn:hover {
            background: #b91c1c;
            box-shadow: 0 2px 4px rgba(220, 38, 38, 0.4);
        }

        .clear-filters-btn:active {
            transform: translateY(0);
        }

        .filter-summary {
            margin-top: 1rem;
            padding: 0.75rem 1rem;
            background: rgba(30, 58, 138, 0.1);
            border: 1px solid rgba(30, 58, 138, 0.2);
            border-radius: 8px;
            font-size: 0.875rem;
            color: #1e3a8a;
        }

        .filter-summary-text {
            font-weight: 500;
        }

        /* Table row animations */
        .table-row-fade-in {
            opacity: 0;
            transform: translateY(10px);
            animation: fadeInUp 0.4s ease forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Enhanced table hover effects */
        .students-table tr:hover {
            background: #f8fafc;
            transition: all 0.2s ease;
        }

        /* Loading state for search */
        .search-input.loading {
            background-image: linear-gradient(90deg, transparent, rgba(30, 58, 138, 0.1), transparent);
            background-size: 200% 100%;
            animation: searchLoading 1.5s infinite;
        }

        @keyframes searchLoading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* User Menu Additional Styles - Override with higher z-index */
        #user-menu-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            z-index: 9998 !important; /* Higher than table elements, lower than loading overlay */
            transition: opacity 0.3s ease;
        }

        #user-menu {
            z-index: 9999 !important; /* Same as loading overlay to ensure it's on top */
        }

        #user-menu-button {
            position: relative;
            z-index: 51; /* Above header, below user menu */
        }

        #user-menu-backdrop.hidden {
            display: none;
        }

        /* Ensure backdrop covers all content including scrollable areas */
        #user-menu-backdrop.show {
            opacity: 1;
            visibility: visible;
        }

        #user-menu-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 50%;
            color: #64748b;
            transition: all 0.3s ease;
        }

        #user-menu-close:hover {
            background: rgba(0, 0, 0, 0.1);
            color: #374151;
        }

        #user-menu-close svg {
            width: 1.25rem;
            height: 1.25rem;
        }

        .menu-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1.5rem;
            color: #374151;
            text-decoration: none;
            transition: all 0.3s ease;
            border-bottom: 1px solid #f1f5f9;
        }

        .menu-item:hover {
            background: rgba(30, 58, 138, 0.05);
            color: #1e3a8a;
        }

        .credits-section, .subscription-section {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .credits-header, .subscription-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #64748b;
            font-size: 0.875rem;
        }

        .credits-balance, .subscription-status {
            color: #1e3a8a;
            font-weight: 600;
        }

        /* Assessment Links Section Styles */
        .assessment-links-section {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f1f5f9;
        }

        .assessment-links-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .assessment-links-list {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .assessment-link-row {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .assessment-link-row:hover {
            background: rgba(30, 58, 138, 0.05);
        }

        .assessment-link-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0.75rem;
            color: #6b7280;
            text-decoration: none;
            font-size: 0.8125rem;
            flex: 1;
            transition: all 0.2s ease;
        }

        .assessment-link-item:hover {
            color: #1e3a8a;
        }

        .assessment-link-item svg {
            flex-shrink: 0;
        }

        .assessment-copy-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem;
            background: transparent;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.2s ease;
            margin-right: 0.25rem;
        }

        .assessment-copy-btn:hover {
            background: rgba(30, 58, 138, 0.1);
            color: #1e3a8a;
        }

        .assessment-copy-btn:active {
            transform: scale(0.95);
        }

        .assessment-copy-btn svg {
            flex-shrink: 0;
        }

        /* Focus states for accessibility */
        .assessment-badge:focus,
        button:focus,
        a:focus,
        .search-input:focus,
        .filter-select:focus,
        .menu-item:focus {
            outline: 2px solid #1e3a8a;
            outline-offset: 2px;
        }

        /* Learning Pathway Button Styles */
        .learning-pathway-btn {
            background: transparent;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 6px;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: none;
        }

        .learning-pathway-btn:hover {
            background: #f9fafb;
            border-color: #1e40af;
            color: #1e40af;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(30, 64, 175, 0.1);
        }

        .learning-pathway-btn:active {
            transform: translateY(0);
            background: #f3f4f6;
            box-shadow: 0 1px 2px rgba(30, 64, 175, 0.1);
        }

        /* Learning Pathway Modal Specific Styles */
        .assessment-summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .assessment-summary-card {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.2s ease;
        }

        .assessment-summary-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .assessment-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .assessment-card-header h4 {
            color: #333333;
            font-size: 16px;
            font-weight: 600;
            margin: 0;
        }

        .assessment-status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .assessment-status-badge.passed {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
            border: 1px solid #4CAF50;
        }

        .assessment-status-badge.needs-improvement {
            background: rgba(255, 152, 0, 0.2);
            color: #FF9800;
            border: 1px solid #FF9800;
        }

        .assessment-card-details {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .assessment-detail {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .detail-label {
            color: #666666;
            font-size: 14px;
            font-weight: 500;
        }

        .detail-value {
            color: #333333;
            font-size: 14px;
            font-weight: 600;
        }

        .pathway-recommendations-grid {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-bottom: 24px;
        }

        .pathway-recommendation-card {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.2s ease;
        }

        .pathway-recommendation-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .pathway-header h4 {
            color: #1976D2;
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 12px 0;
        }

        .pathway-reason {
            margin-bottom: 16px;
        }

        .pathway-reason p {
            color: #666666;
            font-size: 14px;
            line-height: 1.5;
            margin: 0;
        }

        .pathway-courses h5 {
            color: #333333;
            font-size: 14px;
            font-weight: 600;
            margin: 0 0 8px 0;
        }

        .pathway-courses ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .pathway-courses li {
            color: #555555;
            font-size: 14px;
            line-height: 1.5;
            padding: 4px 0;
            padding-left: 16px;
            position: relative;
        }

        .pathway-courses li::before {
            content: '•';
            color: #4CAF50;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .next-steps-container {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .next-step-item {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            padding: 16px;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            transition: all 0.2s ease;
        }

        .next-step-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .step-number {
            background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .step-content {
            color: #333333;
            font-size: 14px;
            line-height: 1.5;
            flex: 1;
        }

        .assessment-no-data {
            text-align: center;
            padding: 40px 20px;
            color: #666666;
            font-style: italic;
        }

        /* Assessment Links Section Styles */
        .assessment-links-grid {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-bottom: 24px;
        }

        .assessment-link-card {
            background: #fff8e1;
            border: 1px solid #ffcc02;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.2s ease;
        }

        .assessment-link-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 204, 2, 0.2);
        }

        .assessment-link-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .assessment-link-header h4 {
            color: #333333;
            font-size: 16px;
            font-weight: 600;
            margin: 0;
        }

        .assessment-status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .assessment-status-badge.incomplete {
            background: #fef3c7;
            color: #d97706;
            border: 1px solid #fbbf24;
        }

        .assessment-instructions {
            color: #666666;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 16px;
        }

        .assessment-link-container {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .assessment-link-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #f8f9fa;
            color: #333333;
            cursor: text;
        }

        .assessment-link-input:focus {
            outline: none;
            border-color: #1976D2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
        }

        .copy-link-button {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: #1976D2;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .copy-link-button:hover {
            background: #1565C0;
            transform: translateY(-1px);
        }

        .copy-link-button:active {
            transform: translateY(0);
        }

        .assessment-links-note {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
        }

        .assessment-links-note p {
            color: #1565C0;
            font-size: 14px;
            margin: 0;
            line-height: 1.5;
        }

        /* Detailed Analysis Styles */
        .assessment-detailed-analysis {
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #e0e0e0;
        }

        .analysis-content {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-top: 8px;
            color: #333333;
            font-size: 14px;
            line-height: 1.6;
            border-left: 4px solid #1976D2;
        }

        /* AI Loading and Skeleton Loader Styles */
        .ai-loading-container {
            padding: 20px;
        }

        .ai-loading-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 32px;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            border: 1px solid #e0e0e0;
        }

        .ai-loading-icon {
            flex-shrink: 0;
        }

        .ai-loading-text h3 {
            color: #333333;
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 8px 0;
        }

        .ai-loading-text p {
            color: #666666;
            font-size: 14px;
            margin: 0;
            line-height: 1.5;
        }

        /* Skeleton Loader Base Styles */
        .skeleton-line {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.5s infinite;
            border-radius: 4px;
        }

        @keyframes skeleton-loading {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        .skeleton-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .skeleton-card {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
        }

        .skeleton-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .skeleton-title {
            width: 120px;
            height: 20px;
        }

        .skeleton-badge {
            width: 80px;
            height: 24px;
            border-radius: 12px;
        }

        .skeleton-details {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .skeleton-detail {
            width: 100%;
            height: 16px;
        }

        .skeleton-recommendations {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-bottom: 24px;
        }

        .skeleton-recommendation {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
        }

        .skeleton-pathway-title {
            width: 150px;
            height: 20px;
            margin-bottom: 12px;
        }

        .skeleton-reason {
            width: 80%;
            height: 16px;
            margin-bottom: 16px;
        }

        .skeleton-courses {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .skeleton-course {
            width: 90%;
            height: 16px;
        }

        .skeleton-next-steps {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .skeleton-step {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
        }

        .skeleton-step-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .skeleton-step-content {
            width: 85%;
            height: 16px;
            flex: 1;
        }

        /* Personalized Message Styles */
        .personalized-message {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            padding: 20px;
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            border: 1px solid #4CAF50;
            border-radius: 12px;
            margin-bottom: 16px;
        }

        .message-icon {
            color: #4CAF50;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .personalized-message p {
            color: #2e7d32;
            font-size: 16px;
            line-height: 1.6;
            margin: 0;
            font-weight: 500;
        }

        /* Enhanced transitions for content updates */
        #learning-pathway-content {
            transition: opacity 0.3s ease-in-out;
        }

        /* Responsive adjustments for learning pathway modal */
        @media (max-width: 768px) {
            .assessment-summary-grid,
            .skeleton-grid {
                grid-template-columns: 1fr;
            }

            .next-step-item,
            .skeleton-step {
                flex-direction: column;
                align-items: center;
                text-align: center;
                gap: 12px;
            }

            .ai-loading-header {
                flex-direction: column;
                text-align: center;
                gap: 12px;
            }

            .skeleton-step-content {
                width: 100%;
            }
        }
    </style>
</head>
<body class="simplified-dashboard">
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
    </div>

    <!-- Top Navigation Bar -->
    <header class="bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50">
        <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo Only -->
                <div class="flex items-center">
                    <img src="logo.png" alt="Skills Assess Logo" class="h-8 w-auto">
                </div>

                <!-- User Menu -->
                <div class="relative">
                    <button id="user-menu-button" class="rounded-full p-2" title="Toggle User Menu">
                        <img id="user-menu-avatar" alt="Avatar" class="rounded-full" src="profile.png" width="32" height="32">
                    </button>

                    <!-- User Menu Backdrop -->
                    <div id="user-menu-backdrop" class="hidden"></div>

                    <!-- User Menu Sidebar -->
                    <div id="user-menu" class="hidden">
                        <!-- Close Button -->
                        <div id="user-menu-close">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                        </div>

                        <!-- User Profile Section -->
                        <div class="px-4 py-2">
                            <div class="flex items-center space-x-3">
                                <div class="avatar-container">
                                    <img id="user-menu-profile-pic" src="profile.png" alt="Profile Picture" class="w-12 h-12 rounded-full">
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p id="user-menu-name" class="font-semibold text-gray-900 truncate">Loading...</p>
                                    <p id="user-menu-email" class="text-xs text-gray-500 truncate">Loading...</p>
                                    <p id="user-menu-company" class="text-xs text-gray-400 truncate">Loading...</p>
                                    <div class="inline-flex items-center mt-1">
                                        <span class="text-xs text-green-600">Active</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Credits Section -->
                        <div class="credits-section">
                            <div class="credits-header">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                                <span>Credits</span>
                            </div>
                            <div class="credits-balance">
                                <span class="font-semibold">0</span>
                            </div>
                        </div>

                        <!-- Subscription Section -->
                        <div class="subscription-section">
                            <div class="subscription-header">
                                <img src="sparkling.png" alt="Subscription" class="w-4 h-4">
                                <span>Subscription</span>
                            </div>
                            <div class="subscription-status">
                                <span class="font-semibold">Loading...</span>
                            </div>
                        </div>

                        <!-- Assessment Links Section -->
                        <div class="assessment-links-section">
                            <div class="assessment-links-header">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M12 14l9-5-9-5-9 5 9 5z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
                                </svg>
                                <span>Assessment Links</span>
                            </div>
                            <div class="assessment-links-list">
                                <div class="assessment-link-row">
                                    <a href="https://english-assessment-bnab.onrender.com/SGA.html" target="_blank" class="assessment-link-item">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                        </svg>
                                        <span>English Assessment</span>
                                    </a>
                                    <button class="assessment-copy-btn" onclick="copyAssessmentLinkFromMenu('https://english-assessment-bnab.onrender.com/SGA.html', 'English Assessment')" title="Copy link">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div class="assessment-link-row">
                                    <a href="https://maths-ds-assessment.onrender.com/math.html" target="_blank" class="assessment-link-item">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                        </svg>
                                        <span>Mathematics Assessment</span>
                                    </a>
                                    <button class="assessment-copy-btn" onclick="copyAssessmentLinkFromMenu('https://maths-ds-assessment.onrender.com/math.html', 'Mathematics Assessment')" title="Copy link">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div class="assessment-link-row">
                                    <a href="https://maths-ds-assessment.onrender.com/digitalSkillsAssessment.html" target="_blank" class="assessment-link-item">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                        </svg>
                                        <span>Digital Skills Assessment</span>
                                    </a>
                                    <button class="assessment-copy-btn" onclick="copyAssessmentLinkFromMenu('https://maths-ds-assessment.onrender.com/digitalSkillsAssessment.html', 'Digital Skills Assessment')" title="Copy link">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Edit Profile Option -->
                        <a href="#" id="edit-profile" class="menu-item">
                            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Profile Settings
                        </a>

                        <!-- Logout Option -->
                        <a id="user-logout" href="#" class="menu-item border-t border-gray-200">
                            <img src="logout.png" alt="Logout" class="w-3 h-3 mr-1">
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="pt-20">
        <div class="dashboard-container">
            <!-- Data Summary Cards -->
            <div class="summary-cards-container loading" id="summary-cards-container">
                <!-- Skeleton Loaders -->
                <div class="summary-card-skeleton">
                    <div class="skeleton-card-icon"></div>
                    <div class="skeleton-card-content">
                        <div class="skeleton-card-value"></div>
                        <div class="skeleton-card-label"></div>
                    </div>
                </div>

                <div class="summary-card-skeleton">
                    <div class="skeleton-card-icon"></div>
                    <div class="skeleton-card-content">
                        <div class="skeleton-card-value"></div>
                        <div class="skeleton-card-label"></div>
                        <div class="skeleton-card-percentage"></div>
                    </div>
                </div>

                <div class="summary-card-skeleton">
                    <div class="skeleton-card-icon"></div>
                    <div class="skeleton-card-content">
                        <div class="skeleton-card-value"></div>
                        <div class="skeleton-card-label"></div>
                        <div class="skeleton-card-percentage"></div>
                    </div>
                </div>

                <div class="summary-card-skeleton">
                    <div class="skeleton-card-icon"></div>
                    <div class="skeleton-card-content">
                        <div class="skeleton-card-value"></div>
                        <div class="skeleton-card-label"></div>
                        <div class="skeleton-card-percentage"></div>
                    </div>
                </div>

                <div class="summary-card-skeleton">
                    <div class="skeleton-card-icon"></div>
                    <div class="skeleton-card-content">
                        <div class="skeleton-card-value"></div>
                        <div class="skeleton-card-label"></div>
                        <div class="skeleton-card-percentage"></div>
                    </div>
                </div>

                <!-- Real Cards -->
                <div class="summary-card" id="total-students-card">
                    <div class="summary-card-icon">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <div class="summary-card-content">
                        <div class="summary-card-value" id="total-students-value">0</div>
                        <div class="summary-card-label">Total Students</div>
                    </div>
                </div>

                <div class="summary-card" id="english-completed-card">
                    <div class="summary-card-icon">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="summary-card-content">
                        <div class="summary-card-value" id="english-completed-value">0</div>
                        <div class="summary-card-label">English Completed</div>
                        <div class="summary-card-percentage" id="english-completed-percentage">0%</div>
                    </div>
                </div>

                <div class="summary-card" id="math-completed-card">
                    <div class="summary-card-icon">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="summary-card-content">
                        <div class="summary-card-value" id="math-completed-value">0</div>
                        <div class="summary-card-label">Math Completed</div>
                        <div class="summary-card-percentage" id="math-completed-percentage">0%</div>
                    </div>
                </div>

                <div class="summary-card" id="digital-skills-completed-card">
                    <div class="summary-card-icon">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="summary-card-content">
                        <div class="summary-card-value" id="digital-skills-completed-value">0</div>
                        <div class="summary-card-label">Digital Skills Completed</div>
                        <div class="summary-card-percentage" id="digital-skills-completed-percentage">0%</div>
                    </div>
                </div>

                <div class="summary-card" id="overall-completion-card">
                    <div class="summary-card-icon">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div class="summary-card-content">
                        <div class="summary-card-value" id="overall-completion-value">0%</div>
                        <div class="summary-card-label">Overall Progress</div>
                        <div class="summary-card-sublabel" id="recent-activity">No recent activity</div>
                    </div>
                </div>
            </div>

            <!-- Students Table -->
            <div class="students-table-container">
                <!-- Search and Filter Section -->
                <div class="search-filter-section">
                    <div class="search-filter-row">
                        <!-- Search Bar -->
                        <div class="search-bar-container">
                            <div class="search-input-wrapper">
                                <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                <input
                                    type="text"
                                    id="student-search"
                                    placeholder="Search by name or email..."
                                    class="search-input"
                                    autocomplete="off"
                                >
                                <button id="clear-search" class="clear-search-btn" style="display: none;">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Filter Dropdowns -->
                        <div class="filter-controls">
                            <select id="completion-filter" class="filter-select">
                                <option value="all">All Students</option>
                                <option value="english-completed">Completed English</option>
                                <option value="math-completed">Completed Math</option>
                                <option value="digital-skills-completed">Completed Digital Skills</option>
                                <option value="all-completed">Completed All Assessments</option>
                                <option value="none-completed">Not Completed</option>
                            </select>

                            <select id="results-filter" class="filter-select">
                                <option value="all">All Results</option>
                                <option value="english-pass">Passed English</option>
                                <option value="english-fail">Failed English</option>
                                <option value="math-pass">Passed Math</option>
                                <option value="math-fail">Failed Math</option>
                                <option value="digital-skills-high">High Digital Skills</option>
                                <option value="digital-skills-low">Low Digital Skills</option>
                            </select>

                            <button id="clear-filters" class="clear-filters-btn">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                Clear Filters
                            </button>
                        </div>
                    </div>

                    <!-- Filter Results Summary -->
                    <div class="filter-summary" id="filter-summary" style="display: none;">
                        <span class="filter-summary-text"></span>
                    </div>
                </div>

                <!-- Scrollable Table Container -->
                <div class="table-scroll-container">
                    <table class="students-table">
                        <thead>
                            <tr>
                                <th>Student Name</th>
                                <th>Email</th>
                                <th>English Assessment</th>
                                <th>Mathematics Assessment</th>
                                <th>Digital Skills Assessment</th>
                                <th>Learning Pathway</th>
                            </tr>
                        </thead>
                        <tbody id="students-table-body">
                            <!-- Loading skeleton rows -->
                            <tr>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader narrow"></div></td>
                            </tr>
                            <tr>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader narrow"></div></td>
                            </tr>
                            <tr>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader wide"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader medium"></div></td>
                                <td><div class="skeleton-loader narrow"></div></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts - Load in proper order for dependencies -->
    <script src="feedback.js"></script>
    <script src="courseSelection.js"></script>
    <script src="stripe-handler.js"></script>
    <script src="accountmanagement.js"></script>
    <script src="skills-gap-modal.js"></script>
    <script src="english-results-modal.js"></script>
    <script src="english-assessment-review-modal.js"></script>
    <script src="math-results-modal.js"></script>
    <script src="math-assessment-review-modal.js"></script>
    <script src="enhanced-math-assessment-modal.js"></script>
    <script src="digital-skills-results-modal.js"></script>
    <script src="warning-modal.js"></script>
    <script src="feature-access-modal.js"></script>
    <script src="subscription-check.js"></script>
    <script src="subscription-modal.js"></script>
    <script src="topup-modal.js"></script>
    <script src="user-menu.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
            authDomain: "barefoot-elearning-app.firebaseapp.com",
            projectId: "barefoot-elearning-app",
            databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
            storageBucket: "barefoot-elearning-app.appspot.com",
            messagingSenderId: "170819735788",
            appId: "1:170819735788:web:223af318437eb5d947d5c9"
        };

        // Initialize Firebase
        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }
        const db = firebase.firestore();

        // Global variables
        let currentUser = null;
        let userCompany = null;
        let studentsData = [];
        let filteredStudentsData = [];
        let currentSearchTerm = '';
        let currentFilters = {
            completion: 'all',
            results: 'all'
        };

        // Utility functions
        function showLoadingOverlay() {
            document.getElementById('loading-overlay').style.display = 'flex';
        }

        function hideLoadingOverlay() {
            document.getElementById('loading-overlay').style.display = 'none';
        }

        function formatDate(timestamp) {
            if (!timestamp) return 'Never';

            let date;
            if (timestamp.toDate) {
                date = timestamp.toDate();
            } else if (timestamp instanceof Date) {
                date = timestamp;
            } else {
                date = new Date(timestamp);
            }

            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        // English assessment badge generation
        function getEnglishAssessmentBadge(englishData) {
            if (!englishData || !englishData.englishAssessmentCompleted) {
                return {
                    html: '<span class="assessment-badge badge-not-completed">Not Completed</span>',
                    clickable: false
                };
            }

            const score = englishData.englishProficiencyScore || 0;
            const level = englishData.englishProficiencyLevel || 'Entry';
            const isPass = score >= 16;

            const badgeClass = isPass ? 'badge-pass' : 'badge-fail';
            const statusText = `${level} (${score}/21)`;

            return {
                html: `<span class="assessment-badge ${badgeClass} english-badge-clickable"
                             data-user-email="${englishData.userEmail}"
                             data-user-name="${englishData.userName}"
                             title="Click to view detailed English assessment results">
                         ${statusText}
                       </span>`,
                clickable: true
            };
        }

        // Mathematics assessment badge generation - Enhanced for new modal
        function getMathAssessmentBadge(mathData) {
            if (!mathData || !mathData.mathAssessmentCompleted) {
                return {
                    html: '<span class="assessment-badge badge-not-completed" title="Mathematics assessment not completed">Not Completed</span>',
                    clickable: false
                };
            }

            const score = mathData.mathOverallScore || 0;
            const level = mathData.mathHighestLevelCompleted || 'Entry';

            // Determine performance level for better visual indication
            let badgeClass = 'badge-completed';
            let performanceIndicator = '';

            // Calculate approximate percentage based on level completed
            const levelMaxScores = { 'Entry': 44, 'Level1': 70, 'GCSEPart1': 80, 'GCSEPart2': 100 };
            const maxPossible = levelMaxScores[level] || 44;
            const percentage = maxPossible > 0 ? Math.round((score / maxPossible) * 100) : 0;

            if (percentage >= 80) {
                badgeClass = 'badge-excellent';
                performanceIndicator = '<svg class="badge-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
            } else if (percentage >= 60) {
                badgeClass = 'badge-good';
                performanceIndicator = '<svg class="badge-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
            } else if (percentage >= 40) {
                badgeClass = 'badge-fair';
                performanceIndicator = '<svg class="badge-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>';
            } else {
                badgeClass = 'badge-needs-work';
                performanceIndicator = '<svg class="badge-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path></svg>';
            }

            return {
                html: `<span class="assessment-badge ${badgeClass} math-badge-clickable"
                             data-user-email="${mathData.userEmail}"
                             data-user-name="${mathData.userName}"
                             title="Click to view comprehensive Mathematics assessment analysis - ${level} Level (${percentage}% performance)">
                         ${performanceIndicator} ${level}
                       </span>`,
                clickable: true
            };
        }

        // Digital Skills assessment badge generation
        function getDigitalSkillsAssessmentBadge(digitalData) {
            if (!digitalData || !digitalData.digitalSkillsAssessmentCompleted) {
                return {
                    html: '<span class="assessment-badge badge-not-completed" title="Digital Skills assessment not completed">Not Completed</span>',
                    clickable: false
                };
            }

            const score = digitalData.digitalSkillsOverallScore || 0;
            const currentLevel = digitalData.digitalSkillsCurrentLevel || 'EntryLevel2';
            const skillsLevel = digitalData.digitalSkillsSkillsLevel || 'EntryLevel2';

            // Determine performance level based on AI-recommended skills level
            let badgeClass = 'badge-completed';
            let performanceIndicator = '';

            // Map skills levels to performance indicators
            switch (skillsLevel) {
                case 'Level3':
                case 'ICDLLevel3':
                    badgeClass = 'badge-excellent';
                    performanceIndicator = '<svg class="badge-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
                    break;
                case 'Level2':
                case 'ICDLLevel2':
                    badgeClass = 'badge-excellent';
                    performanceIndicator = '<svg class="badge-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
                    break;
                case 'Level1':
                    badgeClass = 'badge-good';
                    performanceIndicator = '<svg class="badge-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
                    break;
                case 'EntryLevel3':
                    badgeClass = 'badge-fair';
                    performanceIndicator = '<svg class="badge-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>';
                    break;
                case 'EntryLevel2Plus':
                    badgeClass = 'badge-fair';
                    performanceIndicator = '<svg class="badge-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>';
                    break;
                case 'EntryLevel2':
                default:
                    badgeClass = 'badge-needs-work';
                    performanceIndicator = '<svg class="badge-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path></svg>';
                    break;
            }

            // Format display level name
            const displayLevel = currentLevel.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()).trim();

            return {
                html: `<span class="assessment-badge ${badgeClass} digital-badge-clickable"
                             data-user-email="${digitalData.userEmail}"
                             data-user-name="${digitalData.userName}"
                             title="Click to view comprehensive Digital Skills assessment analysis - ${displayLevel} (AI Level: ${skillsLevel})">
                         ${performanceIndicator} ${skillsLevel}
                       </span>`,
                clickable: true
            };
        }

        // Load students data from Firebase
        async function loadStudentsData() {
            try {
                showLoadingOverlay();
                showSummaryCardsLoading();

                if (!userCompany) {
                    throw new Error('No company information available');
                }

                const companyRef = db.collection('companies').doc(userCompany);
                const usersSnapshot = await companyRef.collection('users').get();

                studentsData = [];

                for (const doc of usersSnapshot.docs) {
                    const userData = doc.data();
                    const userEmail = doc.id;

                    // Filter for students only
                    if (userData.userType !== 'student') continue;

                    const studentInfo = {
                        email: userEmail,
                        name: `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || 'Unknown',
                        firstName: userData.firstName || '',
                        lastName: userData.lastName || '',

                        // English assessment data
                        english: {
                            englishAssessmentCompleted: userData.englishAssessmentCompleted || false,
                            englishProficiencyScore: userData.englishProficiencyScore || 0,
                            englishProficiencyLevel: userData.englishProficiencyLevel || 'Entry',
                            englishAssessmentTimestamp: userData.englishAssessmentTimestamp,
                            timeSpentOnEnglish: userData.timeSpentOnEnglish || 0,
                            englishResponse: userData.englishResponse || '',
                            englishFeedback: userData.englishFeedback || {},
                            englishStrengths: userData.englishStrengths || [],
                            englishImprovements: userData.englishImprovements || [],
                            englishAssessmentResponses: userData.englishAssessmentResponses || null,
                            manualScoreOverride: userData.manualScoreOverride || null,
                            manualLevelOverride: userData.manualLevelOverride || null,
                            userEmail: userEmail,
                            userName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || 'Unknown'
                        },

                        // Mathematics assessment data
                        math: {
                            mathAssessmentCompleted: userData.mathAssessmentCompleted || false,
                            mathOverallScore: userData.mathOverallScore || 0,
                            mathCurrentLevel: userData.mathCurrentLevel || 'Not determined',
                            mathHighestLevelCompleted: userData.mathHighestLevelCompleted || 'Entry',
                            mathAssessmentTimestamp: userData.mathAssessmentTimestamp,
                            totalTimeSpentOnMath: userData.totalTimeSpentOnMath || 0,
                            mathEntryLevel: userData.mathEntryLevel || null,
                            mathLevel1: userData.mathLevel1 || null,
                            mathGCSEPart1: userData.mathGCSEPart1 || null,
                            mathGCSEPart2: userData.mathGCSEPart2 || null,
                            mathFeedback: userData.mathFeedback || {},
                            mathStrengths: userData.mathStrengths || [],
                            mathImprovements: userData.mathImprovements || [],
                            manualScoreOverride: userData.manualScoreOverride || null,
                            manualLevelOverride: userData.manualLevelOverride || null,
                            userEmail: userEmail,
                            userName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || 'Unknown'
                        },

                        // Digital Skills assessment data
                        digitalSkills: {
                            digitalSkillsAssessmentCompleted: userData.digitalSkillsAssessmentCompleted || false,
                            digitalSkillsOverallScore: userData.digitalSkillsOverallScore || 0,
                            digitalSkillsCurrentLevel: userData.digitalSkillsCurrentLevel || 'EntryLevel2',
                            digitalSkillsSkillsLevel: userData.digitalSkillsSkillsLevel || 'EntryLevel2',
                            digitalSkillsHighestLevelCompleted: userData.digitalSkillsHighestLevelCompleted || null,
                            digitalSkillsAssessmentTimestamp: userData.digitalSkillsAssessmentTimestamp,
                            totalTimeSpentOnDigitalSkills: userData.totalTimeSpentOnDigitalSkills || 0,
                            digitalSkillsFeedback: userData.digitalSkillsFeedback || {},
                            digitalSkillsStrengths: userData.digitalSkillsStrengths || [],
                            digitalSkillsImprovements: userData.digitalSkillsImprovements || [],
                            digitalSkillsLevelProgression: userData.digitalSkillsLevelProgression || null,
                            manualScoreOverride: userData.manualScoreOverride || null,
                            manualLevelOverride: userData.manualLevelOverride || null,
                            userEmail: userEmail,
                            userName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || 'Unknown'
                        },

                        // Activity data
                        lastActivity: userData.lastUpdated || userData.createdAt || null,
                        createdAt: userData.createdAt
                    };

                    studentsData.push(studentInfo);
                }

                // Sort by name
                studentsData.sort((a, b) => a.name.localeCompare(b.name));

                // Initialize filtered data
                filteredStudentsData = [...studentsData];

                renderStudentsTable();
                updateStudentsCount();
                updateSummaryCards();
                initializeSearchAndFilters();

            } catch (error) {
                console.error('Error loading students data:', error);
                showErrorMessage('Failed to load student data. Please refresh the page.');
            } finally {
                hideLoadingOverlay();
            }
        }

        // Render students table
        function renderStudentsTable() {
            const tbody = document.getElementById('students-table-body');

            if (filteredStudentsData.length === 0) {
                const message = studentsData.length === 0
                    ? 'No students found in your organization.'
                    : 'No students match the current search and filter criteria.';

                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center py-8 text-gray-500">
                            ${message}
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredStudentsData.map((student, index) => {
                const englishBadge = getEnglishAssessmentBadge(student.english);
                const mathBadge = getMathAssessmentBadge(student.math);
                const digitalSkillsBadge = getDigitalSkillsAssessmentBadge(student.digitalSkills);
                const learningPathwayIcon = getLearningPathwayIcon(student);

                return `
                    <tr style="animation-delay: ${index * 0.05}s" class="table-row-fade-in">
                        <td class="font-medium text-gray-900">${student.name}</td>
                        <td class="text-gray-600">${student.email}</td>
                        <td>${englishBadge.html}</td>
                        <td>${mathBadge.html}</td>
                        <td>${digitalSkillsBadge.html}</td>
                        <td>${learningPathwayIcon}</td>
                    </tr>
                `;
            }).join('');

            // Add click handlers for assessment badges and learning pathway buttons
            addBadgeClickHandlers();
            addLearningPathwayClickHandlers();
        }

        // Update students count (now handled by summary cards)
        function updateStudentsCount() {
            // This function is now handled by updateSummaryCards()
            // Keeping for backward compatibility but functionality moved
        }

        // Show summary cards loading state
        function showSummaryCardsLoading() {
            const container = document.getElementById('summary-cards-container');
            if (container) {
                container.classList.add('loading');
            }
        }

        // Hide summary cards loading state
        function hideSummaryCardsLoading() {
            const container = document.getElementById('summary-cards-container');
            if (container) {
                container.classList.remove('loading');
            }
        }

        // Update summary cards with real data
        function updateSummaryCards() {
            const totalStudents = studentsData.length;
            const englishCompleted = studentsData.filter(s => s.english.englishAssessmentCompleted).length;
            const mathCompleted = studentsData.filter(s => s.math.mathAssessmentCompleted).length;
            const digitalSkillsCompleted = studentsData.filter(s => s.digitalSkills.digitalSkillsAssessmentCompleted).length;
            const allThreeCompleted = studentsData.filter(s =>
                s.english.englishAssessmentCompleted &&
                s.math.mathAssessmentCompleted &&
                s.digitalSkills.digitalSkillsAssessmentCompleted
            ).length;

            // Calculate percentages
            const englishPercentage = totalStudents > 0 ? Math.round((englishCompleted / totalStudents) * 100) : 0;
            const mathPercentage = totalStudents > 0 ? Math.round((mathCompleted / totalStudents) * 100) : 0;
            const digitalSkillsPercentage = totalStudents > 0 ? Math.round((digitalSkillsCompleted / totalStudents) * 100) : 0;
            const overallPercentage = totalStudents > 0 ? Math.round((allThreeCompleted / totalStudents) * 100) : 0;

            // Update total students card
            document.getElementById('total-students-value').textContent = totalStudents;

            // Update English completed card
            document.getElementById('english-completed-value').textContent = englishCompleted;
            document.getElementById('english-completed-percentage').textContent = `${englishPercentage}%`;

            // Update Math completed card
            document.getElementById('math-completed-value').textContent = mathCompleted;
            document.getElementById('math-completed-percentage').textContent = `${mathPercentage}%`;

            // Update Digital Skills completed card
            document.getElementById('digital-skills-completed-value').textContent = digitalSkillsCompleted;
            document.getElementById('digital-skills-completed-percentage').textContent = `${digitalSkillsPercentage}%`;

            // Update overall completion card
            document.getElementById('overall-completion-value').textContent = `${overallPercentage}%`;

            // Update recent activity
            const recentActivityElement = document.getElementById('recent-activity');
            if (totalStudents === 0) {
                recentActivityElement.textContent = 'No students enrolled';
            } else if (allThreeCompleted === 0) {
                recentActivityElement.textContent = 'No assessments completed';
            } else {
                const recentCompletions = studentsData.filter(s => {
                    const hasRecent = s.english.englishAssessmentTimestamp || s.math.mathAssessmentTimestamp || s.digitalSkills.digitalSkillsAssessmentTimestamp;
                    if (!hasRecent) return false;

                    const englishDate = s.english.englishAssessmentTimestamp ?
                        (s.english.englishAssessmentTimestamp.toDate ? s.english.englishAssessmentTimestamp.toDate() : new Date(s.english.englishAssessmentTimestamp)) : null;
                    const mathDate = s.math.mathAssessmentTimestamp ?
                        (s.math.mathAssessmentTimestamp.toDate ? s.math.mathAssessmentTimestamp.toDate() : new Date(s.math.mathAssessmentTimestamp)) : null;
                    const digitalSkillsDate = s.digitalSkills.digitalSkillsAssessmentTimestamp ?
                        (s.digitalSkills.digitalSkillsAssessmentTimestamp.toDate ? s.digitalSkills.digitalSkillsAssessmentTimestamp.toDate() : new Date(s.digitalSkills.digitalSkillsAssessmentTimestamp)) : null;

                    const mostRecent = [englishDate, mathDate, digitalSkillsDate]
                        .filter(date => date !== null)
                        .reduce((latest, current) => latest && current ? (latest > current ? latest : current) : (latest || current), null);

                    if (!mostRecent) return false;

                    const daysDiff = (new Date() - mostRecent) / (1000 * 60 * 60 * 24);
                    return daysDiff <= 7; // Within last 7 days
                }).length;

                if (recentCompletions > 0) {
                    recentActivityElement.textContent = `${recentCompletions} completed this week`;
                } else {
                    recentActivityElement.textContent = 'No recent activity';
                }
            }

            // Hide loading state and show real cards
            hideSummaryCardsLoading();

            // Add animation to cards
            const cards = document.querySelectorAll('.summary-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }

        // Search and filter functions
        function filterStudents() {
            let filtered = [...studentsData];

            // Apply search filter
            if (currentSearchTerm.trim()) {
                const searchLower = currentSearchTerm.toLowerCase().trim();
                filtered = filtered.filter(student => {
                    const nameMatch = student.name.toLowerCase().includes(searchLower);
                    const emailMatch = student.email.toLowerCase().includes(searchLower);
                    const firstNameMatch = student.firstName.toLowerCase().includes(searchLower);
                    const lastNameMatch = student.lastName.toLowerCase().includes(searchLower);

                    return nameMatch || emailMatch || firstNameMatch || lastNameMatch;
                });
            }

            // Apply completion filter
            if (currentFilters.completion !== 'all') {
                filtered = filtered.filter(student => {
                    switch (currentFilters.completion) {
                        case 'english-completed':
                            return student.english.englishAssessmentCompleted;
                        case 'math-completed':
                            return student.math.mathAssessmentCompleted;
                        case 'digital-skills-completed':
                            return student.digitalSkills.digitalSkillsAssessmentCompleted;
                        case 'all-completed':
                            return student.english.englishAssessmentCompleted &&
                                   student.math.mathAssessmentCompleted &&
                                   student.digitalSkills.digitalSkillsAssessmentCompleted;
                        case 'none-completed':
                            return !student.english.englishAssessmentCompleted &&
                                   !student.math.mathAssessmentCompleted &&
                                   !student.digitalSkills.digitalSkillsAssessmentCompleted;
                        default:
                            return true;
                    }
                });
            }

            // Apply results filter
            if (currentFilters.results !== 'all') {
                filtered = filtered.filter(student => {
                    switch (currentFilters.results) {
                        case 'english-pass':
                            return student.english.englishAssessmentCompleted && student.english.englishProficiencyScore >= 16;
                        case 'english-fail':
                            return student.english.englishAssessmentCompleted && student.english.englishProficiencyScore < 16;
                        case 'math-pass':
                            return student.math.mathAssessmentCompleted && student.math.mathOverallScore > 0;
                        case 'math-fail':
                            return student.math.mathAssessmentCompleted && student.math.mathOverallScore === 0;
                        case 'digital-skills-high':
                            return student.digitalSkills.digitalSkillsAssessmentCompleted &&
                                   ['Level2', 'Level3', 'ICDLLevel2', 'ICDLLevel3'].includes(student.digitalSkills.digitalSkillsSkillsLevel);
                        case 'digital-skills-low':
                            return student.digitalSkills.digitalSkillsAssessmentCompleted &&
                                   ['EntryLevel2', 'EntryLevel2Plus', 'EntryLevel3', 'Level1'].includes(student.digitalSkills.digitalSkillsSkillsLevel);
                        default:
                            return true;
                    }
                });
            }

            filteredStudentsData = filtered;
            renderStudentsTable();
            updateStudentsCount();
            updateFilterSummary();
        }

        // Update filter summary
        function updateFilterSummary() {
            const summaryElement = document.getElementById('filter-summary');
            const summaryText = summaryElement.querySelector('.filter-summary-text');

            const hasSearch = currentSearchTerm.trim() !== '';
            const hasCompletionFilter = currentFilters.completion !== 'all';
            const hasResultsFilter = currentFilters.results !== 'all';

            if (hasSearch || hasCompletionFilter || hasResultsFilter) {
                let summary = 'Showing ';
                const parts = [];

                if (hasSearch) {
                    parts.push(`search: "${currentSearchTerm}"`);
                }

                if (hasCompletionFilter) {
                    const completionLabels = {
                        'english-completed': 'completed English',
                        'math-completed': 'completed Math',
                        'both-completed': 'completed both assessments',
                        'none-completed': 'no completed assessments'
                    };
                    parts.push(`filter: ${completionLabels[currentFilters.completion]}`);
                }

                if (hasResultsFilter) {
                    const resultsLabels = {
                        'english-pass': 'passed English',
                        'english-fail': 'failed English',
                        'math-pass': 'passed Math',
                        'math-fail': 'failed Math'
                    };
                    parts.push(`results: ${resultsLabels[currentFilters.results]}`);
                }

                summary += parts.join(', ');
                summaryText.textContent = summary;
                summaryElement.style.display = 'block';
            } else {
                summaryElement.style.display = 'none';
            }
        }

        // Initialize search and filter functionality
        function initializeSearchAndFilters() {
            const searchInput = document.getElementById('student-search');
            const clearSearchBtn = document.getElementById('clear-search');
            const completionFilter = document.getElementById('completion-filter');
            const resultsFilter = document.getElementById('results-filter');
            const clearFiltersBtn = document.getElementById('clear-filters');

            // Search input event listener with debouncing
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const value = this.value;

                // Show/hide clear button
                if (value.trim()) {
                    clearSearchBtn.style.display = 'flex';
                } else {
                    clearSearchBtn.style.display = 'none';
                }

                // Debounce search
                searchTimeout = setTimeout(() => {
                    currentSearchTerm = value;
                    filterStudents();
                }, 300);
            });

            // Clear search button
            clearSearchBtn.addEventListener('click', function() {
                searchInput.value = '';
                currentSearchTerm = '';
                this.style.display = 'none';
                filterStudents();
                searchInput.focus();
            });

            // Filter dropdowns
            completionFilter.addEventListener('change', function() {
                currentFilters.completion = this.value;
                filterStudents();
            });

            resultsFilter.addEventListener('change', function() {
                currentFilters.results = this.value;
                filterStudents();
            });

            // Clear filters button
            clearFiltersBtn.addEventListener('click', function() {
                // Reset all filters
                searchInput.value = '';
                completionFilter.value = 'all';
                resultsFilter.value = 'all';
                clearSearchBtn.style.display = 'none';

                // Reset state
                currentSearchTerm = '';
                currentFilters = {
                    completion: 'all',
                    results: 'all'
                };

                // Apply filters
                filterStudents();

                // Focus search input
                searchInput.focus();

                // Show success message
                showSuccessMessage('All filters cleared');
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', function(event) {
                // Ctrl/Cmd + F to focus search
                if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
                    event.preventDefault();
                    searchInput.focus();
                    searchInput.select();
                }

                // Escape to clear search when focused
                if (event.key === 'Escape' && document.activeElement === searchInput) {
                    if (searchInput.value) {
                        clearSearchBtn.click();
                    } else {
                        searchInput.blur();
                    }
                }
            });
        }

        // Generate learning pathway icon
        function getLearningPathwayIcon(student) {
            // Check if at least one assessment is completed
            const hasCompletedAssessment = student.english.englishAssessmentCompleted ||
                                         student.math.mathAssessmentCompleted ||
                                         student.digitalSkills.digitalSkillsAssessmentCompleted;

            if (!hasCompletedAssessment) {
                return `<span class="text-gray-400 text-sm">No assessments completed</span>`;
            }

            return `
                <button class="learning-pathway-btn"
                        data-user-email="${student.email}"
                        data-user-name="${student.name}"
                        title="View AI-Powered Learning Pathway Recommendations">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <!-- Graduation cap -->
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 14l9-5-9-5-9 5 9 5z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 14v7"/>
                        <!-- Tassel -->
                        <circle cx="16" cy="6" r="1" fill="currentColor"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                              d="M16 7l1 2"/>
                    </svg>
                </button>
            `;
        }

        // Add click handlers for assessment badges
        function addBadgeClickHandlers() {
            // English assessment badge handlers
            document.querySelectorAll('.english-badge-clickable').forEach(badge => {
                badge.addEventListener('click', async function(event) {
                    event.preventDefault();
                    const userEmail = this.getAttribute('data-user-email');
                    const userName = this.getAttribute('data-user-name');

                    console.log('English badge clicked for:', userEmail, userName);

                    // Check if English Results Modal is available
                    if (typeof window.EnglishResultsModal !== 'undefined') {
                        try {
                            showLoadingOverlay();
                            await window.EnglishResultsModal.show({}, userEmail, userName, userCompany);
                            showSuccessMessage('English assessment details loaded');
                        } catch (error) {
                            console.error('Error showing English results modal:', error);
                            showErrorMessage('Failed to load English assessment details');
                        } finally {
                            hideLoadingOverlay();
                        }
                    } else {
                        console.error('EnglishResultsModal not found');
                        showErrorMessage('English assessment modal is not available. Please refresh the page.');
                    }
                });
            });

            // Mathematics assessment badge handlers - Enhanced Modal Integration
            // Prioritizes the Enhanced Mathematics Assessment Modal for comprehensive analytics
            document.querySelectorAll('.math-badge-clickable').forEach(badge => {
                badge.addEventListener('click', async function(event) {
                    event.preventDefault();
                    const userEmail = this.getAttribute('data-user-email');
                    const userName = this.getAttribute('data-user-name');

                    console.log('Math badge clicked for:', userEmail, userName, '- Using Enhanced Modal');

                    // Prioritize Enhanced Math Assessment Modal for admin users
                    const enhancedMathModal = window.EnhancedMathAssessmentModal;
                    const fallbackMathModal = window.MathResultsModal || window.MathAssessmentReviewModal || window.MathematicsResultsModal;

                    if (enhancedMathModal && typeof enhancedMathModal.show === 'function') {
                        try {
                            showLoadingOverlay();
                            await enhancedMathModal.show({}, userEmail, userName, userCompany);
                            showSuccessMessage('Enhanced mathematics assessment analysis loaded');
                        } catch (error) {
                            console.error('Error showing Enhanced Math modal:', error);
                            // Fallback to original modal if enhanced modal fails
                            if (fallbackMathModal && typeof fallbackMathModal.show === 'function') {
                                try {
                                    await fallbackMathModal.show({}, userEmail, userName, userCompany);
                                    showSuccessMessage('Mathematics assessment details loaded');
                                } catch (fallbackError) {
                                    console.error('Error showing fallback Math modal:', fallbackError);
                                    showErrorMessage('Failed to load Mathematics assessment details');
                                }
                            } else {
                                showErrorMessage('Failed to load Mathematics assessment details');
                            }
                        } finally {
                            hideLoadingOverlay();
                        }
                    } else if (fallbackMathModal && typeof fallbackMathModal.show === 'function') {
                        try {
                            showLoadingOverlay();
                            await fallbackMathModal.show({}, userEmail, userName, userCompany);
                            showSuccessMessage('Mathematics assessment details loaded');
                        } catch (error) {
                            console.error('Error showing Math results modal:', error);
                            showErrorMessage('Failed to load Mathematics assessment details');
                        } finally {
                            hideLoadingOverlay();
                        }
                    } else {
                        console.error('Math modal not found. Available:', {
                            EnhancedMathAssessmentModal: typeof window.EnhancedMathAssessmentModal,
                            MathResultsModal: typeof window.MathResultsModal,
                            MathAssessmentReviewModal: typeof window.MathAssessmentReviewModal,
                            MathematicsResultsModal: typeof window.MathematicsResultsModal
                        });
                        showErrorMessage('Mathematics assessment modal is not available. Please refresh the page.');
                    }
                });
            });

            // Digital Skills assessment badge handlers
            document.querySelectorAll('.digital-badge-clickable').forEach(badge => {
                badge.addEventListener('click', async function(event) {
                    event.preventDefault();
                    const userEmail = this.getAttribute('data-user-email');
                    const userName = this.getAttribute('data-user-name');

                    console.log('Digital Skills badge clicked for:', userEmail, userName);

                    // Check if Digital Skills Results Modal is available
                    const digitalSkillsModal = window.DigitalSkillsResultsModal || window.DigitalSkillsAssessmentModal;

                    if (digitalSkillsModal && typeof digitalSkillsModal.show === 'function') {
                        try {
                            showLoadingOverlay();
                            await digitalSkillsModal.show({}, userEmail, userName, userCompany);
                            showSuccessMessage('Digital Skills assessment details loaded');
                        } catch (error) {
                            console.error('Error showing Digital Skills results modal:', error);
                            showErrorMessage('Failed to load Digital Skills assessment details');
                        } finally {
                            hideLoadingOverlay();
                        }
                    } else {
                        // Show Digital Skills Results Modal
                        if (window.DigitalSkillsResultsModal) {
                            window.DigitalSkillsResultsModal.show(
                                student.digitalSkills,
                                student.digitalSkills.userEmail,
                                student.digitalSkills.userName,
                                'Birmingham'
                            );
                        } else {
                            console.log('Digital Skills modal not available. User:', userName, 'Email:', userEmail);
                            showSuccessMessage('Digital Skills assessment completed! Loading detailed results...');
                        }
                    }
                });
            });
        }

        // Add click handlers for learning pathway buttons
        function addLearningPathwayClickHandlers() {
            const buttons = document.querySelectorAll('.learning-pathway-btn');
            console.log('Found learning pathway buttons:', buttons.length);

            buttons.forEach(button => {
                button.addEventListener('click', async function(event) {
                    event.preventDefault();
                    const userEmail = this.getAttribute('data-user-email');
                    const userName = this.getAttribute('data-user-name');

                    console.log('Learning pathway button clicked for:', userEmail, userName);
                    await showLearningPathwayModal(userEmail, userName);
                });
            });
        }

        // Show error message with improved styling
        function showErrorMessage(message) {
            const toast = document.createElement('div');
            toast.className = 'toast-notification toast-error';
            toast.textContent = message;
            document.body.appendChild(toast);

            // Trigger animation
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            // Remove toast
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 300);
            }, 5000);
        }

        // Show success message with improved styling
        function showSuccessMessage(message) {
            const toast = document.createElement('div');
            toast.className = 'toast-notification toast-success';
            toast.textContent = message;
            document.body.appendChild(toast);

            // Trigger animation
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            // Remove toast
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 300);
            }, 3000);
        }

        // Show learning pathway modal with OpenAI recommendations
        async function showLearningPathwayModal(userEmail, userName) {
            try {
                console.log('showLearningPathwayModal called with:', userEmail, userName);
                console.log('studentsData length:', studentsData.length);

                // Find student data
                const student = studentsData.find(s => s.email === userEmail);
                console.log('Found student:', student);

                if (!student) {
                    console.error('Student not found:', userEmail);
                    showErrorMessage('Student data not found. Please refresh the page and try again.');
                    return;
                }

                // Create modal with skeleton loaders immediately
                createLearningPathwayModalWithSkeletons(userName);

                // Generate AI-powered recommendations
                const recommendationsResult = await generateAILearningPathwayRecommendations(student);
                console.log('Generated AI recommendations:', recommendationsResult);

                // Update modal with actual content
                updateLearningPathwayModalContent(recommendationsResult.recommendations, recommendationsResult.fromCache);

            } catch (error) {
                console.error('Error showing learning pathway modal:', error);
                showErrorMessage('Failed to load learning pathway recommendations. Please try again.');
                // Hide modal on error
                hideLearningPathwayModal();
            }
        }

        // Generate a hash of assessment data to detect changes
        function generateAssessmentDataHash(student) {
            const assessmentData = {
                english: {
                    completed: student.english?.englishAssessmentCompleted || false,
                    score: student.english?.englishProficiencyScore || 0,
                    level: student.english?.englishProficiencyLevel || '',
                    timestamp: student.english?.englishAssessmentTimestamp || null
                },
                mathematics: {
                    completed: student.mathematics?.mathAssessmentCompleted || false,
                    score: student.mathematics?.mathScore || 0,
                    level: student.mathematics?.mathLevel || '',
                    timestamp: student.mathematics?.mathAssessmentTimestamp || null
                },
                digitalSkills: {
                    completed: student.digitalSkills?.digitalSkillsAssessmentCompleted || false,
                    score: student.digitalSkills?.digitalSkillsScore || 0,
                    level: student.digitalSkills?.digitalSkillsLevel || '',
                    timestamp: student.digitalSkills?.digitalSkillsAssessmentTimestamp || null
                }
            };

            // Create a simple hash from the assessment data
            const dataString = JSON.stringify(assessmentData);
            let hash = 0;
            for (let i = 0; i < dataString.length; i++) {
                const char = dataString.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32-bit integer
            }
            return hash.toString();
        }

        // Check for cached AI response
        async function checkCachedAIResponse(student) {
            try {
                const currentHash = generateAssessmentDataHash(student);
                const cacheRef = db.collection('aiResponseCache').doc(student.email);
                const cacheDoc = await cacheRef.get();

                if (cacheDoc.exists) {
                    const cacheData = cacheDoc.data();

                    // Check if the assessment data hash matches (no changes in assessment data)
                    if (cacheData.assessmentDataHash === currentHash) {
                        console.log('Found valid cached AI response for:', student.email);
                        return cacheData.aiResponse;
                    } else {
                        console.log('Assessment data changed, cache invalid for:', student.email);
                        // Delete outdated cache
                        await cacheRef.delete();
                    }
                }

                return null;
            } catch (error) {
                console.error('Error checking cached AI response:', error);
                return null;
            }
        }

        // Cache AI response in Firebase
        async function cacheAIResponse(student, aiResponse) {
            try {
                const assessmentDataHash = generateAssessmentDataHash(student);
                const cacheRef = db.collection('aiResponseCache').doc(student.email);

                await cacheRef.set({
                    studentEmail: student.email,
                    studentName: student.name,
                    aiResponse: aiResponse,
                    assessmentDataHash: assessmentDataHash,
                    cachedAt: firebase.firestore.FieldValue.serverTimestamp(),
                    lastUpdated: firebase.firestore.FieldValue.serverTimestamp()
                });

                console.log('AI response cached successfully for:', student.email);
            } catch (error) {
                console.error('Error caching AI response:', error);
                // Don't throw error - caching failure shouldn't break the main flow
            }
        }

        // Clear cached AI response for a specific student (useful for testing or when data is manually updated)
        async function clearCachedAIResponse(studentEmail) {
            try {
                const cacheRef = db.collection('aiResponseCache').doc(studentEmail);
                await cacheRef.delete();
                console.log('Cached AI response cleared for:', studentEmail);
            } catch (error) {
                console.error('Error clearing cached AI response:', error);
            }
        }

        // Clear all cached AI responses (admin function)
        async function clearAllCachedAIResponses() {
            try {
                const cacheCollection = db.collection('aiResponseCache');
                const snapshot = await cacheCollection.get();

                const batch = db.batch();
                snapshot.docs.forEach(doc => {
                    batch.delete(doc.ref);
                });

                await batch.commit();
                console.log('All cached AI responses cleared');
            } catch (error) {
                console.error('Error clearing all cached AI responses:', error);
            }
        }

        // Generate AI-powered learning pathway recommendations using OpenAI GPT-4o
        async function generateAILearningPathwayRecommendations(student) {
            try {
                // Check for cached AI response first
                const cachedResponse = await checkCachedAIResponse(student);
                if (cachedResponse) {
                    console.log('Using cached AI response for student:', student.email);
                    return {
                        recommendations: cachedResponse,
                        fromCache: true
                    };
                }

                // Prepare comprehensive learner data for OpenAI with all raw assessment details
                const learnerData = {
                    name: student.name,
                    email: student.email,
                    assessments: {
                        english: {
                            // Basic completion status
                            completed: student.english.englishAssessmentCompleted || false,

                            // Core assessment results
                            score: student.english.englishProficiencyScore || 0,
                            level: student.english.englishProficiencyLevel || 'Not determined',
                            maxScore: 21,

                            // Detailed assessment data (if completed)
                            ...(student.english.englishAssessmentCompleted ? {
                                // Timing and completion data
                                timeSpent: student.english.timeSpentOnEnglish || 0,
                                completedAt: student.english.englishAssessmentTimestamp || null,

                                // Detailed feedback and analysis
                                feedback: student.english.englishFeedback || {},
                                strengths: student.english.englishStrengths || [],
                                improvements: student.english.englishImprovements || [],

                                // Raw response data
                                response: student.english.englishResponse || '',

                                // Assessment responses (if available)
                                assessmentResponses: student.english.englishAssessmentResponses || null,

                                // Manual overrides (if any)
                                manualScoreOverride: student.english.manualScoreOverride || null,
                                manualLevelOverride: student.english.manualLevelOverride || null
                            } : {})
                        },
                        mathematics: {
                            // Basic completion status
                            completed: student.math.mathAssessmentCompleted || false,

                            // Core assessment results
                            overallScore: student.math.mathOverallScore || 0,
                            currentLevel: student.math.mathCurrentLevel || 'Not determined',
                            highestLevelCompleted: student.math.mathHighestLevelCompleted || 'None',

                            // Detailed assessment data (if completed)
                            ...(student.math.mathAssessmentCompleted ? {
                                // Timing and completion data
                                totalTimeSpent: student.math.totalTimeSpentOnMath || 0,
                                completedAt: student.math.mathAssessmentTimestamp || null,

                                // Level-specific detailed breakdowns
                                mathEntryLevel: student.math.mathEntryLevel || null,
                                mathLevel1: student.math.mathLevel1 || null,
                                mathGCSEPart1: student.math.mathGCSEPart1 || null,
                                mathGCSEPart2: student.math.mathGCSEPart2 || null,

                                // Performance analysis
                                feedback: student.math.mathFeedback || {},
                                strengths: student.math.mathStrengths || [],
                                improvements: student.math.mathImprovements || [],

                                // Manual overrides (if any)
                                manualScoreOverride: student.math.manualScoreOverride || null,
                                manualLevelOverride: student.math.manualLevelOverride || null
                            } : {})
                        },
                        digitalSkills: {
                            // Basic completion status
                            completed: student.digitalSkills.digitalSkillsAssessmentCompleted || false,

                            // Core assessment results
                            overallScore: student.digitalSkills.digitalSkillsOverallScore || 0,
                            currentLevel: student.digitalSkills.digitalSkillsCurrentLevel || 'Not determined',
                            skillsLevel: student.digitalSkills.digitalSkillsSkillsLevel || 'Not determined',
                            highestLevelCompleted: student.digitalSkills.digitalSkillsHighestLevelCompleted || 'None',

                            // Detailed assessment data (if completed)
                            ...(student.digitalSkills.digitalSkillsAssessmentCompleted ? {
                                // Timing and completion data
                                totalTimeSpent: student.digitalSkills.totalTimeSpentOnDigitalSkills || 0,
                                completedAt: student.digitalSkills.digitalSkillsAssessmentTimestamp || null,

                                // Detailed feedback and analysis
                                feedback: student.digitalSkills.digitalSkillsFeedback || {},
                                strengths: student.digitalSkills.digitalSkillsStrengths || [],
                                improvements: student.digitalSkills.digitalSkillsImprovements || [],

                                // Level progression data
                                levelProgression: student.digitalSkills.digitalSkillsLevelProgression || null,

                                // Manual overrides (if any)
                                manualScoreOverride: student.digitalSkills.manualScoreOverride || null,
                                manualLevelOverride: student.digitalSkills.manualLevelOverride || null
                            } : {})
                        }
                    },

                    // Assessment completion status for handling incomplete assessments
                    incompleteAssessments: {
                        english: !student.english.englishAssessmentCompleted,
                        mathematics: !student.math.mathAssessmentCompleted,
                        digitalSkills: !student.digitalSkills.digitalSkillsAssessmentCompleted
                    }
                };

                // Debug: Log the comprehensive data being sent to AI
                console.log('Comprehensive learner data being sent to AI:', JSON.stringify(learnerData, null, 2));

                // Create the prompt for OpenAI
                const prompt = `You are an educational pathway advisor for Skills Assess, a platform that helps learners progress through different skill levels. You are reviewing comprehensive learner assessment results that have already been completed and analysed. Based on this assessment data, provide personalized eligible course recommendations and next steps.

COMPREHENSIVE LEARNER DATA:
${JSON.stringify(learnerData, null, 2)}

ASSESSMENT LINKS FOR INCOMPLETE ASSESSMENTS:
- Mathematics Assessment: https://maths-ds-assessment.onrender.com/math.html
- Digital Skills Assessment: https://maths-ds-assessment.onrender.com/digitalSkillsAssessment.html
- English Assessment: https://english-assessment-bnab.onrender.com/SGA.html

PATHWAY PROGRESSION RULES (for completed assessments only):

English Pathway:
- Level 2 (score 16-21) → Level 3 Health and Social Care (H&SC) course, Level 3 Digital Skills Course or below
- Level 1 (score 10-15) → Level 2 English, Level 2 digital skills and below, Level 2 Health and SC
- Entry Level 3 (score 0-9) → Level 1 English, Entry 3 Health & SC
- Entry Level 2 or below → Beginners or Beginners Plus

Mathematics Pathway:
- Level 2/GCSE → Level 3 Health and SC, Level 3 Digital Skills Course or below
- Level 1 → Level 2 Maths, Level 2 Digital Skills and below, Level 2 Health and SC
- Entry Level 3 → Level 1 Maths, Level 1 Digital Course, Entry 3 Health & SC
- Entry Level 2 or below → Beginners or Beginners Plus

ICT/Digital Skills Pathway:
- Level 2 → Level 3 Digital Skills Course or below
- Level 1 → Level 2 digital skills and below
- Entry Level 3 → Level 1 Digital Course or below
- Entry Level 2 or below → Beginners or Beginners Plus

IMPORTANT INSTRUCTIONS:
- For INCOMPLETE assessments, do NOT provide learning pathway recommendations
- Instead, include assessment links in the "assessmentLinks" section
- Use all available detailed data (feedback, strengths, improvements, timing) to provide comprehensive analysis
- Focus on completed assessments for pathway recommendations
- Provide clear next steps for both completed and incomplete assessments

Please provide a response in the following JSON format:
{
  "assessmentsSummary": [
    {
      "type": "English/Mathematics/Digital Skills",
      "level": "current level or 'Not Completed'",
      "score": "score/maxScore or 'Not Completed'",
      "status": "Level 2 Achieved/Below Level 2/Not Completed",
      "detailedAnalysis": "comprehensive analysis using all available data including feedback, strengths, improvements, timing"
    }
  ],
  "pathwayRecommendations": [
    {
      "pathway": "pathway name",
      "courses": ["list of eligible courses"],
      "reason": "explanation for eligibility based on detailed assessment data"
    }
  ],
  "assessmentLinks": [
    {
      "assessmentType": "Mathematics/Digital Skills/English",
      "link": "assessment URL",
      "instructions": "Clear instructions for admin to share with student"
    }
  ],
  "nextSteps": [
    "personalized next step 1 (including assessment completion if needed)",
    "personalized next step 2",
    "personalized next step 3"
  ],
  "personalizedMessage": "A motivational message tailored to the learner's current progress and potential, acknowledging both completed and incomplete assessments"
}

Focus on being encouraging, specific, and actionable. Use all available detailed assessment data to provide comprehensive insights. For incomplete assessments, provide clear guidance on next steps. Remember: you are reviewing assessment results, not conducting assessments yourself.`;

                // Make API call to main server's OpenAI endpoint
                const response = await fetch('/api/openai/learning-pathway', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        learnerData: learnerData
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    console.error('OpenAI API response:', errorData);
                    throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${errorData.error || 'Unknown error'}`);
                }

                const recommendations = await response.json();
                console.log('Successfully received AI recommendations:', recommendations);

                // Cache the AI response for future use
                await cacheAIResponse(student, recommendations);

                return {
                    recommendations: recommendations,
                    fromCache: false
                };

            } catch (error) {
                console.error('Error generating AI recommendations:', error);

                // Show user-friendly error message for different error types
                if (error.message.includes('CORS') || error.message.includes('fetch') || error.message.includes('Failed to fetch')) {
                    console.warn('Network error detected. Using fallback recommendations.');
                    showErrorMessage('AI recommendations temporarily unavailable. Using standard recommendations.');
                } else if (error.message.includes('500') || error.message.includes('Internal server error')) {
                    console.warn('Server error detected. Using fallback recommendations.');
                    showErrorMessage('AI service temporarily unavailable. Using standard recommendations.');
                } else {
                    showErrorMessage('Failed to generate AI recommendations. Using standard recommendations.');
                }

                // Fallback to static recommendations if AI fails
                return {
                    recommendations: generateFallbackRecommendations(student),
                    fromCache: false
                };
            }
        }

        // Fallback recommendations if OpenAI fails
        function generateFallbackRecommendations(student) {
            const recommendations = {
                assessmentsSummary: [],
                pathwayRecommendations: [],
                nextSteps: [],
                personalizedMessage: "The student's assessment results have been reviewed. Based on their current levels, the following recommendations have been prepared to support their learning progression."
            };

            // English Assessment Analysis
            if (student.english.englishAssessmentCompleted) {
                const englishLevel = student.english.englishProficiencyLevel || 'Entry';
                const englishScore = student.english.englishProficiencyScore || 0;

                recommendations.assessmentsSummary.push({
                    type: 'English',
                    level: englishLevel
                });

                // English pathway recommendations
                if (englishScore >= 16) {
                    recommendations.pathwayRecommendations.push({
                        pathway: 'English Pathway',
                        courses: ['Level 3 Health and Social Care (H&SC) course', 'Level 3 Digital Skills Course or below'],
                        reason: 'The student has achieved Level 2 English and is eligible for Level 3 courses'
                    });
                } else if (englishLevel.includes('L1') || englishLevel.includes('Level 1')) {
                    recommendations.pathwayRecommendations.push({
                        pathway: 'English Pathway',
                        courses: ['Level 2 English', 'Level 2 Digital Skills and below', 'Level 2 Health and Social Care'],
                        reason: 'The student has achieved Level 1 English and is eligible for Level 2 courses'
                    });
                } else {
                    recommendations.pathwayRecommendations.push({
                        pathway: 'English Pathway',
                        courses: ['Level 1 English', 'Entry Level 3 Health & Social Care', 'Beginners courses'],
                        reason: 'The student is at Entry level and should focus on building foundational skills'
                    });
                }
            }

            // Mathematics Assessment Analysis
            if (student.math.mathAssessmentCompleted) {
                const mathLevel = student.math.mathHighestLevelCompleted || 'Entry';

                recommendations.assessmentsSummary.push({
                    type: 'Mathematics',
                    level: mathLevel
                });

                if (mathLevel.includes('Level 2') || mathLevel.includes('GCSE')) {
                    recommendations.pathwayRecommendations.push({
                        pathway: 'Mathematics Pathway',
                        courses: ['Level 3 Health and Social Care', 'Level 3 Digital Skills Course or below'],
                        reason: 'The student has achieved Level 2 Mathematics and is eligible for Level 3 courses'
                    });
                } else if (mathLevel.includes('Level 1')) {
                    recommendations.pathwayRecommendations.push({
                        pathway: 'Mathematics Pathway',
                        courses: ['Level 2 Mathematics', 'Level 2 Digital Skills and below', 'Level 2 Health and Social Care'],
                        reason: 'The student has achieved Level 1 Mathematics and is eligible for Level 2 courses'
                    });
                } else {
                    recommendations.pathwayRecommendations.push({
                        pathway: 'Mathematics Pathway',
                        courses: ['Level 1 Mathematics', 'Level 1 Digital Course', 'Entry Level 3 Health & Social Care', 'Beginners courses'],
                        reason: 'The student is at Entry level and should focus on building foundational skills'
                    });
                }
            }

            // Digital Skills Assessment Analysis
            if (student.digitalSkills.digitalSkillsAssessmentCompleted) {
                const digitalLevel = student.digitalSkills.digitalSkillsCurrentLevel || 'EntryLevel2';

                recommendations.assessmentsSummary.push({
                    type: 'Digital Skills (ICT)',
                    level: digitalLevel
                });

                if (digitalLevel.includes('Level2')) {
                    recommendations.pathwayRecommendations.push({
                        pathway: 'ICT Pathway',
                        courses: ['Level 3 Digital Skills Course or below'],
                        reason: 'The student has achieved Level 2 ICT and is eligible for Level 3 digital courses'
                    });
                } else if (digitalLevel.includes('Level1')) {
                    recommendations.pathwayRecommendations.push({
                        pathway: 'ICT Pathway',
                        courses: ['Level 2 Digital Skills and below'],
                        reason: 'The student has achieved Level 1 ICT and is eligible for Level 2 digital courses'
                    });
                } else {
                    recommendations.pathwayRecommendations.push({
                        pathway: 'ICT Pathway',
                        courses: ['Level 1 Digital Course or below', 'Beginners courses'],
                        reason: 'The student is at Entry level and should focus on building foundational digital skills'
                    });
                }
            }

            // Generate next steps
            if (recommendations.assessmentsSummary.length === 0) {
                recommendations.nextSteps.push('The student should complete at least one assessment to receive personalised eligible course recommendations');
                recommendations.nextSteps.push('Recommend starting with the English assessment to establish their current level');
                recommendations.nextSteps.push('Consider encouraging the student to take the Mathematics and Digital Skills assessments for comprehensive guidance');
            } else {
                recommendations.nextSteps.push('Review the eligible courses with the student based on their assessment results');
                recommendations.nextSteps.push('Guide the student to start with the highest level courses they are eligible for');
                recommendations.nextSteps.push('Consider encouraging the student to complete additional assessments for more comprehensive eligible course recommendations');

                // Add specific next steps based on completed assessments
                const completedCount = recommendations.assessmentsSummary.length;
                if (completedCount < 3) {
                    const remaining = ['English', 'Mathematics', 'Digital Skills'].filter(type =>
                        !recommendations.assessmentsSummary.some(assessment => assessment.type.includes(type))
                    );
                    if (remaining.length > 0) {
                        recommendations.nextSteps.push(`Encourage the student to complete the ${remaining.join(' and ')} assessment${remaining.length > 1 ? 's' : ''} for a complete learning pathway`);
                    }
                }
            }

            return recommendations;
        }



        // Create learning pathway modal with skeleton loaders
        function createLearningPathwayModalWithSkeletons(userName) {
            console.log('Creating learning pathway modal with skeletons for:', userName);

            // Remove existing modal if present
            const existingModal = document.getElementById('learning-pathway-overlay');
            if (existingModal) {
                existingModal.remove();
            }

            // Create modal HTML with skeleton loaders
            const modalHTML = `
                <div id="learning-pathway-overlay" class="assessment-modal-overlay">
                    <div class="assessment-modal-content">
                        <div class="assessment-modal-header">
                            <div class="assessment-modal-title-container">
                                <h2 class="assessment-modal-student-title">${userName}</h2>
                                <h3 class="assessment-modal-subtitle">Learning Pathway Recommendations</h3>
                            </div>
                            <div class="assessment-modal-actions">
                                <button id="close-learning-pathway-modal" class="assessment-close-modal-button">
                                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <div class="assessment-modal-body" id="learning-pathway-content">
                            ${createSkeletonLoaders()}
                        </div>
                    </div>
                </div>
            `;

            // Add modal to DOM
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // Add event listeners
            const overlay = document.getElementById('learning-pathway-overlay');
            const closeButton = document.getElementById('close-learning-pathway-modal');

            if (!overlay || !closeButton) {
                console.error('Modal elements not found after creation');
                return;
            }

            closeButton.addEventListener('click', () => {
                hideLearningPathwayModal();
            });

            overlay.addEventListener('click', (event) => {
                if (event.target.id === 'learning-pathway-overlay') {
                    hideLearningPathwayModal();
                }
            });

            // Show modal with animation
            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.assessment-modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }
            }, 50);
        }

        // Create skeleton loaders for the modal
        function createSkeletonLoaders() {
            return `
                <div class="ai-loading-container">
                    <div class="ai-loading-header">
                        <div class="ai-loading-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="#4CAF50"/>
                                <animateTransform attributeName="transform" type="rotate" values="0 12 12;360 12 12" dur="2s" repeatCount="indefinite"/>
                            </svg>
                        </div>
                        <div class="ai-loading-text">
                            <h3>Analyzing your learning journey...</h3>
                            <p>Our AI is reviewing your assessment results to create personalized recommendations</p>
                        </div>
                    </div>

                    <div class="assessment-section">
                        <h3>Assessment Summary</h3>
                        <div class="skeleton-grid">
                            <div class="skeleton-card">
                                <div class="skeleton-header">
                                    <div class="skeleton-line skeleton-title"></div>
                                    <div class="skeleton-badge"></div>
                                </div>
                                <div class="skeleton-details">
                                    <div class="skeleton-line skeleton-detail"></div>
                                    <div class="skeleton-line skeleton-detail"></div>
                                </div>
                            </div>
                            <div class="skeleton-card">
                                <div class="skeleton-header">
                                    <div class="skeleton-line skeleton-title"></div>
                                    <div class="skeleton-badge"></div>
                                </div>
                                <div class="skeleton-details">
                                    <div class="skeleton-line skeleton-detail"></div>
                                    <div class="skeleton-line skeleton-detail"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="assessment-section">
                        <h3>Eligible Courses</h3>
                        <div class="skeleton-recommendations">
                            <div class="skeleton-recommendation">
                                <div class="skeleton-line skeleton-pathway-title"></div>
                                <div class="skeleton-line skeleton-reason"></div>
                                <div class="skeleton-courses">
                                    <div class="skeleton-line skeleton-course"></div>
                                    <div class="skeleton-line skeleton-course"></div>
                                    <div class="skeleton-line skeleton-course"></div>
                                </div>
                            </div>
                            <div class="skeleton-recommendation">
                                <div class="skeleton-line skeleton-pathway-title"></div>
                                <div class="skeleton-line skeleton-reason"></div>
                                <div class="skeleton-courses">
                                    <div class="skeleton-line skeleton-course"></div>
                                    <div class="skeleton-line skeleton-course"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="assessment-section">
                        <h3>Next Steps</h3>
                        <div class="skeleton-next-steps">
                            <div class="skeleton-step">
                                <div class="skeleton-step-number"></div>
                                <div class="skeleton-line skeleton-step-content"></div>
                            </div>
                            <div class="skeleton-step">
                                <div class="skeleton-step-number"></div>
                                <div class="skeleton-line skeleton-step-content"></div>
                            </div>
                            <div class="skeleton-step">
                                <div class="skeleton-step-number"></div>
                                <div class="skeleton-line skeleton-step-content"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Update modal content with AI recommendations
        function updateLearningPathwayModalContent(recommendations, fromCache = false) {
            const contentContainer = document.getElementById('learning-pathway-content');
            if (!contentContainer) {
                console.error('Content container not found');
                return;
            }

            // Add cache indicator if data came from cache
            const cacheIndicator = fromCache ? `
                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem; padding: 0.5rem; background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 6px; font-size: 0.8125rem; color: #0369a1;">
                    <svg style="width: 1rem; height: 1rem; flex-shrink: 0;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    <span>Using cached recommendations (no assessment changes detected)</span>
                </div>
            ` : '';

            // Create the actual content
            const actualContent = `
                ${cacheIndicator}
                ${createAssessmentsSummarySection(recommendations.assessmentsSummary)}
                ${recommendations.assessmentLinks && recommendations.assessmentLinks.length > 0 ? createAssessmentLinksSection(recommendations.assessmentLinks) : ''}
                ${createPathwayRecommendationsSection(recommendations.pathwayRecommendations)}
                ${createNextStepsSection(recommendations.nextSteps)}
                ${recommendations.personalizedMessage ? createPersonalizedMessageSection(recommendations.personalizedMessage) : ''}
            `;

            // Fade out skeleton, fade in content
            contentContainer.style.opacity = '0';
            setTimeout(() => {
                contentContainer.innerHTML = actualContent;
                contentContainer.style.opacity = '1';
            }, 300);
        }

        // Create personalized message section
        function createPersonalizedMessageSection(message) {
            return `
                <div class="assessment-section">
                    <div class="personalized-message">
                        <div class="message-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                            </svg>
                        </div>
                        <p>${message}</p>
                    </div>
                </div>
            `;
        }

        // Create and display learning pathway modal (legacy function for compatibility)
        function createLearningPathwayModal(userName, recommendations) {
            console.log('Creating learning pathway modal for:', userName, recommendations);

            // Remove existing modal if present
            const existingModal = document.getElementById('learning-pathway-overlay');
            if (existingModal) {
                existingModal.remove();
            }

            // Create modal HTML
            const modalHTML = `
                <div id="learning-pathway-overlay" class="assessment-modal-overlay">
                    <div class="assessment-modal-content">
                        <div class="assessment-modal-header">
                            <div class="assessment-modal-title-container">
                                <h2 class="assessment-modal-student-title">${userName}</h2>
                                <h3 class="assessment-modal-subtitle">Learning Pathway Recommendations</h3>
                            </div>
                            <div class="assessment-modal-actions">
                                <button id="close-learning-pathway-modal" class="assessment-close-modal-button">
                                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <div class="assessment-modal-body">
                            ${createAssessmentsSummarySection(recommendations.assessmentsSummary)}
                            ${createPathwayRecommendationsSection(recommendations.pathwayRecommendations)}
                            ${createNextStepsSection(recommendations.nextSteps)}
                        </div>
                    </div>
                </div>
            `;

            // Add modal to DOM
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // Add event listeners
            const overlay = document.getElementById('learning-pathway-overlay');
            const closeButton = document.getElementById('close-learning-pathway-modal');

            if (!overlay || !closeButton) {
                console.error('Modal elements not found after creation');
                return;
            }

            closeButton.addEventListener('click', () => {
                hideLearningPathwayModal();
            });

            overlay.addEventListener('click', (event) => {
                if (event.target.id === 'learning-pathway-overlay') {
                    hideLearningPathwayModal();
                }
            });

            // Show modal with animation
            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.assessment-modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }
            }, 50);
        }

        // Hide learning pathway modal
        function hideLearningPathwayModal() {
            const overlay = document.getElementById('learning-pathway-overlay');
            if (overlay) {
                overlay.style.opacity = '0';
                const modalContent = overlay.querySelector('.assessment-modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '0';
                    modalContent.style.transform = 'scale(0.95)';
                }
                setTimeout(() => {
                    overlay.remove();
                }, 300);
            }
        }

        // Create assessments summary section
        function createAssessmentsSummarySection(assessmentsSummary) {
            if (assessmentsSummary.length === 0) {
                return `
                    <div class="assessment-section">
                        <h3>Assessment Summary</h3>
                        <div class="assessment-no-data">
                            <p>The student has not completed any assessments yet. At least one assessment must be completed to receive personalised recommendations.</p>
                        </div>
                    </div>
                `;
            }

            const summaryCards = assessmentsSummary.map(assessment => `
                <div class="assessment-summary-card">
                    <div class="assessment-card-header">
                        <h4>${assessment.type}</h4>
                    </div>
                    <div class="assessment-card-details">
                        <div class="assessment-detail">
                            <span class="detail-label">Level Achieved:</span>
                            <span class="detail-value">${assessment.level}</span>
                        </div>
                        ${assessment.detailedAnalysis ? `
                            <div class="assessment-detailed-analysis">
                                <span class="detail-label">Detailed Analysis:</span>
                                <div class="analysis-content">${assessment.detailedAnalysis}</div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `).join('');

            return `
                <div class="assessment-section">
                    <h3>Assessment Summary</h3>
                    <div class="assessment-summary-grid">
                        ${summaryCards}
                    </div>
                </div>
            `;
        }

        // Create assessment links section for incomplete assessments
        function createAssessmentLinksSection(assessmentLinks) {
            if (!assessmentLinks || assessmentLinks.length === 0) {
                return '';
            }

            const linkCards = assessmentLinks.map(assessment => `
                <div class="assessment-link-card">
                    <div class="assessment-link-header">
                        <h4>${assessment.assessmentType} Assessment</h4>
                        <div class="assessment-status-badge incomplete">Not Completed</div>
                    </div>
                    <div class="assessment-link-content">
                        <p class="assessment-instructions">${assessment.instructions}</p>
                        <div class="assessment-link-container">
                            <input type="text" value="${assessment.link}" readonly class="assessment-link-input" id="link-${assessment.assessmentType.replace(/\s+/g, '-').toLowerCase()}">
                            <button class="copy-link-button" onclick="copyAssessmentLink('${assessment.assessmentType.replace(/\s+/g, '-').toLowerCase()}')">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                </svg>
                                Copy Link
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            return `
                <div class="assessment-section">
                    <h3>Incomplete Assessments</h3>
                    <div class="assessment-links-grid">
                        ${linkCards}
                    </div>
                    <div class="assessment-links-note">
                        <p><strong>Note:</strong> Share these assessment links with the student to complete their remaining assessments. Once completed, more comprehensive learning pathway recommendations will be available.</p>
                    </div>
                </div>
            `;
        }

        // Create pathway recommendations section
        function createPathwayRecommendationsSection(pathwayRecommendations) {
            if (pathwayRecommendations.length === 0) {
                return `
                    <div class="assessment-section">
                        <h3>Eligible Courses</h3>
                        <div class="assessment-no-data">
                            <p>The student must complete assessments to receive eligible course recommendations.</p>
                        </div>
                    </div>
                `;
            }

            const recommendationCards = pathwayRecommendations.map(recommendation => `
                <div class="pathway-recommendation-card">
                    <div class="pathway-header">
                        <h4>${recommendation.pathway}</h4>
                    </div>
                    <div class="pathway-reason">
                        <p>${recommendation.reason}</p>
                    </div>
                    <div class="pathway-courses">
                        <h5>Eligible Courses:</h5>
                        <ul>
                            ${recommendation.courses.map(course => `<li>${course}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `).join('');

            return `
                <div class="assessment-section">
                    <h3>Eligible Courses</h3>
                    <div class="pathway-recommendations-grid">
                        ${recommendationCards}
                    </div>
                </div>
            `;
        }

        // Create next steps section
        function createNextStepsSection(nextSteps) {
            const stepsHTML = nextSteps.map((step, index) => `
                <div class="next-step-item">
                    <div class="step-number">${index + 1}</div>
                    <div class="step-content">${step}</div>
                </div>
            `).join('');

            return `
                <div class="assessment-section">
                    <h3>Next Steps</h3>
                    <div class="next-steps-container">
                        ${stepsHTML}
                    </div>
                </div>
            `;
        }

        // Initialize dashboard
        async function initializeDashboard() {
            try {
                // Wait for Firebase auth to be ready
                await new Promise((resolve) => {
                    firebase.auth().onAuthStateChanged((user) => {
                        if (user) {
                            currentUser = user;
                            resolve();
                        } else {
                            // Redirect to login if not authenticated
                            window.location.href = 'index.html';
                        }
                    });
                });

                // Get admin data to find company
                const adminRef = db.collection('Admins').doc(currentUser.email);
                const adminDoc = await adminRef.get();

                if (adminDoc.exists) {
                    const adminData = adminDoc.data();
                    userCompany = adminData.company;

                    // Update user menu with real data
                    updateUserMenuData(currentUser, adminData);

                    if (userCompany) {
                        await loadStudentsData();
                    } else {
                        showErrorMessage('No company information found for your account');
                    }
                } else {
                    showErrorMessage('Admin account not found');
                }

            } catch (error) {
                console.error('Error initializing dashboard:', error);
                showErrorMessage('Failed to initialize dashboard');
            }
        }

        // Handle authentication state changes
        firebase.auth().onAuthStateChanged((user) => {
            if (user) {
                currentUser = user;
                console.log('User authenticated:', user.email);
            } else {
                console.log('User not authenticated, redirecting to login');
                window.location.href = 'index.html';
            }
        });

        // Wait for all scripts to load before initializing
        function waitForScripts() {
            return new Promise((resolve) => {
                let checkCount = 0;
                const maxChecks = 50; // 5 seconds max wait

                const checkScripts = () => {
                    checkCount++;
                    console.log('Checking scripts...', {
                        EnglishResultsModal: typeof window.EnglishResultsModal,
                        MathResultsModal: typeof window.MathResultsModal,
                        MathAssessmentReviewModal: typeof window.MathAssessmentReviewModal,
                        EnhancedMathAssessmentModal: typeof window.EnhancedMathAssessmentModal,
                        checkCount
                    });

                    if (typeof window.EnglishResultsModal !== 'undefined' || checkCount >= maxChecks) {
                        resolve();
                    } else {
                        setTimeout(checkScripts, 100);
                    }
                };

                checkScripts();
            });
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('DOM loaded, waiting for scripts...');
            await waitForScripts();
            console.log('Scripts loaded, starting enhanced initialization...');
            enhancedInitialization();
        });

        // Refresh data function
        function refreshData() {
            if (userCompany) {
                showLoadingOverlay();
                loadStudentsData().then(() => {
                    showSuccessMessage('Data refreshed successfully');
                }).catch(() => {
                    showErrorMessage('Failed to refresh data');
                }).finally(() => {
                    hideLoadingOverlay();
                });
            }
        }

        // Add keyboard shortcut for refresh (Ctrl+R or F5)
        document.addEventListener('keydown', function(event) {
            if ((event.ctrlKey && event.key === 'r') || event.key === 'F5') {
                event.preventDefault();
                refreshData();
            }
        });

        // Debug function to check modal availability
        function checkModalAvailability() {
            const modals = {
                EnglishResultsModal: window.EnglishResultsModal,
                MathResultsModal: window.MathResultsModal,
                MathAssessmentReviewModal: window.MathAssessmentReviewModal,
                EnhancedMathAssessmentModal: window.EnhancedMathAssessmentModal,
                SkillsGapAnalysis: window.SkillsGapAnalysis,
                showSkillsGapAnalysis: window.showSkillsGapAnalysis
            };

            console.log('Modal availability check:', modals);
            return modals;
        }

        // Enhanced initialization with better error handling
        async function enhancedInitialization() {
            try {
                console.log('Starting enhanced initialization...');

                // Check modal availability
                checkModalAvailability();

                // Initialize user menu (from user-menu.js) with delay to ensure DOM is ready
                setTimeout(() => {
                    if (typeof initializeUserMenu === 'function') {
                        console.log('Initializing user menu...');
                        initializeUserMenu();

                        // Verify user menu elements are accessible
                        const userMenuButton = document.getElementById('user-menu-button');
                        const userMenu = document.getElementById('user-menu');
                        const userMenuBackdrop = document.getElementById('user-menu-backdrop');

                        if (userMenuButton && userMenu && userMenuBackdrop) {
                            console.log('User menu elements found and accessible');
                        } else {
                            console.error('User menu elements not found:', {
                                button: !!userMenuButton,
                                menu: !!userMenu,
                                backdrop: !!userMenuBackdrop
                            });
                        }
                    } else {
                        console.error('initializeUserMenu function not found');
                    }
                }, 100);

                // Initialize dashboard
                await initializeDashboard();

                console.log('Dashboard initialized successfully');
                showSuccessMessage('Dashboard loaded successfully');

            } catch (error) {
                console.error('Failed to initialize dashboard:', error);
                showErrorMessage('Failed to initialize dashboard. Please refresh the page.');
            }
        }

        // User menu functionality is handled by user-menu.js

        // Update user menu with real data
        function updateUserMenuData(user, adminData) {
            console.log('Updating user menu data...', adminData);

            // Update user name
            const nameElement = document.getElementById('user-menu-name');
            if (nameElement) {
                nameElement.textContent = `${adminData.firstname || ''} ${adminData.lastname || ''}`.trim() || 'User';
            }

            // Update user email
            const emailElement = document.getElementById('user-menu-email');
            if (emailElement) {
                emailElement.textContent = user.email || 'No email';
            }

            // Update company (display "Skills Assess" instead of "Birmingham" for UI)
            const companyElement = document.getElementById('user-menu-company');
            if (companyElement) {
                const displayCompany = adminData.company === 'Birmingham' ? 'Skills Assess' : adminData.company;
                companyElement.textContent = displayCompany || 'No company';
            }

            // Update profile picture
            const profilePicElement = document.getElementById('user-menu-profile-pic');
            const avatarElement = document.getElementById('user-menu-avatar');
            const profilePicUrl = user.photoURL || adminData.profilePicture || 'profile.png';

            if (profilePicElement) {
                profilePicElement.src = profilePicUrl;
            }
            if (avatarElement) {
                avatarElement.src = profilePicUrl;
            }

            // Update credits
            const creditsBalance = document.querySelector('.credits-balance .font-semibold');
            if (creditsBalance) {
                creditsBalance.textContent = adminData.credits || 0;
            }

            // Update subscription status
            const subscriptionStatus = document.querySelector('.subscription-status .font-semibold');
            if (subscriptionStatus) {
                const isActive = adminData.subscriptionActive;
                const subscriptionType = adminData.subscriptionType;

                if (isActive && subscriptionType) {
                    if (subscriptionType === 'freeTrial') {
                        subscriptionStatus.textContent = 'Free Trial';
                    } else {
                        subscriptionStatus.textContent = 'Active';
                    }
                } else {
                    subscriptionStatus.textContent = 'Inactive';
                }
            }

            // Set up real-time listener for credits updates
            const adminRef = db.collection('Admins').doc(user.email);
            adminRef.onSnapshot((doc) => {
                if (doc.exists) {
                    const data = doc.data();
                    const creditsElement = document.querySelector('.credits-balance .font-semibold');
                    if (creditsElement) {
                        creditsElement.textContent = data.credits || 0;
                    }
                }
            });
        }

        // Export functions for global access
        window.SimplifiedDashboard = {
            refreshData,
            loadStudentsData,
            showErrorMessage,
            showSuccessMessage,
            checkModalAvailability,
            enhancedInitialization
        };

        // Global error handler
        window.addEventListener('error', function(event) {
            console.error('Global error:', event.error);
            showErrorMessage('An unexpected error occurred. Please refresh the page.');
        });

        // Copy assessment link to clipboard
        function copyAssessmentLink(assessmentType) {
            const linkInput = document.getElementById(`link-${assessmentType}`);
            if (!linkInput) {
                console.error('Link input not found for:', assessmentType);
                return;
            }

            // Select and copy the text
            linkInput.select();
            linkInput.setSelectionRange(0, 99999); // For mobile devices

            try {
                document.execCommand('copy');

                // Show success feedback
                const button = linkInput.nextElementSibling;
                if (button) {
                    const originalText = button.innerHTML;
                    button.innerHTML = `
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <polyline points="20,6 9,17 4,12"></polyline>
                        </svg>
                        Copied!
                    `;
                    button.style.backgroundColor = '#10b981';

                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.style.backgroundColor = '';
                    }, 2000);
                }

                showSuccessMessage(`${assessmentType.replace(/-/g, ' ')} assessment link copied to clipboard!`);
            } catch (err) {
                console.error('Failed to copy link:', err);
                showErrorMessage('Failed to copy link. Please copy manually.');
            }
        }

        // Copy assessment link from user menu
        function copyAssessmentLinkFromMenu(url, assessmentName) {
            try {
                // Create a temporary textarea to copy the URL
                const tempTextarea = document.createElement('textarea');
                tempTextarea.value = url;
                document.body.appendChild(tempTextarea);
                tempTextarea.select();
                tempTextarea.setSelectionRange(0, 99999); // For mobile devices

                // Copy to clipboard
                document.execCommand('copy');
                document.body.removeChild(tempTextarea);

                // Find the button that was clicked and show visual feedback
                const buttons = document.querySelectorAll('.assessment-copy-btn');
                buttons.forEach(button => {
                    if (button.getAttribute('onclick').includes(url)) {
                        const originalHTML = button.innerHTML;
                        button.innerHTML = `
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <polyline points="20,6 9,17 4,12"></polyline>
                            </svg>
                        `;
                        button.style.color = '#10b981';
                        button.style.background = 'rgba(16, 185, 129, 0.1)';

                        setTimeout(() => {
                            button.innerHTML = originalHTML;
                            button.style.color = '';
                            button.style.background = '';
                        }, 2000);
                    }
                });

                showSuccessMessage(`${assessmentName} link copied to clipboard!`);
            } catch (err) {
                console.error('Failed to copy link:', err);
                showErrorMessage('Failed to copy link. Please copy manually.');
            }
        }

        // Initialize scroll shadow effect for table container
        function initializeScrollShadow() {
            const scrollContainer = document.querySelector('.table-scroll-container');
            if (scrollContainer) {
                scrollContainer.addEventListener('scroll', function() {
                    if (this.scrollTop > 0) {
                        this.classList.add('scrolled');
                    } else {
                        this.classList.remove('scrolled');
                    }
                });
            }
        }

        // Initialize scroll shadow when DOM is ready
        document.addEventListener('DOMContentLoaded', initializeScrollShadow);

        // Debug function to check user menu state
        function debugUserMenu() {
            const userMenuButton = document.getElementById('user-menu-button');
            const userMenu = document.getElementById('user-menu');
            const userMenuBackdrop = document.getElementById('user-menu-backdrop');

            console.log('User Menu Debug:', {
                button: {
                    exists: !!userMenuButton,
                    visible: userMenuButton ? getComputedStyle(userMenuButton).display !== 'none' : false,
                    zIndex: userMenuButton ? getComputedStyle(userMenuButton).zIndex : 'N/A'
                },
                menu: {
                    exists: !!userMenu,
                    visible: userMenu ? !userMenu.classList.contains('hidden') : false,
                    zIndex: userMenu ? getComputedStyle(userMenu).zIndex : 'N/A'
                },
                backdrop: {
                    exists: !!userMenuBackdrop,
                    visible: userMenuBackdrop ? !userMenuBackdrop.classList.contains('hidden') : false,
                    zIndex: userMenuBackdrop ? getComputedStyle(userMenuBackdrop).zIndex : 'N/A'
                }
            });
        }

        // Make debug function available globally
        window.debugUserMenu = debugUserMenu;

        // Initialize the dashboard when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, starting dashboard initialization...');
            enhancedInitialization();
        });

        // Global unhandled promise rejection handler
        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);
            showErrorMessage('An unexpected error occurred. Please refresh the page.');
        });
    </script>
</body>
</html>
